# 级联选择组件 (Cascade Component)

基于UI截图实现的完整级联选择功能组件，支持服务场景选择、产品详情展示、运营服务明细编辑和商机项目建设清单管理。

## 功能特性

### 🎯 核心功能
- **左侧服务场景列表**：显示各种服务场景，支持勾选和数量显示
- **右侧产品详情区域**：展示服务场景简介、必选产品列表、运营服务明细
- **级联选择机制**：勾选服务场景时，必选产品自动勾选且不可取消
- **数据同步机制**：选择变化自动同步到商机项目建设清单
- **数量编辑功能**：运营服务明细支持实时数量编辑

### 📊 数据管理
- 完整的TypeScript类型定义
- 智能的数据同步和状态管理
- 支持级联关系和实时更新

## 快速开始

### 基本使用

```tsx
import React from 'react';
import { Cascade } from './src';
import { mockServiceScenarios } from './src/data/mockData';

const App: React.FC = () => {
  const handleSelectionChange = (selectedItems) => {
    console.log('选择变化:', selectedItems);
  };

  const handleQuantityChange = (itemId, quantity) => {
    console.log('数量变化:', { itemId, quantity });
  };

  return (
    <Cascade
      initialData={mockServiceScenarios}
      onSelectionChange={handleSelectionChange}
      onQuantityChange={handleQuantityChange}
    />
  );
};
```

### 组件Props

#### Cascade 主组件

```tsx
interface CascadeProps {
  initialData?: ServiceScenario[];           // 初始数据
  onSelectionChange?: (selectedItems: ProjectBuildItem[]) => void; // 选择变化回调
  onQuantityChange?: (itemId: string, quantity: number) => void;   // 数量变化回调
}
```

## 数据结构

### 服务场景 (ServiceScenario)

```tsx
interface ServiceScenario {
  id: string;                              // 唯一标识
  name: string;                            // 场景名称
  count: number;                           // 产品数量
  selected: boolean;                       // 是否被选中
  description: string;                     // 场景简介
  requiredProducts: RequiredProduct[];     // 必选产品列表
  operationalServices: OperationalService[]; // 运营服务明细
}
```

### 运营服务 (OperationalService)

```tsx
interface OperationalService {
  id: string;                              // 唯一标识
  type: string;                            // 运营服务类型
  description: string;                     // 运营服务说明
  unit: string;                            // 计量单位
  quantity: number;                        // 数量
  editable: boolean;                       // 是否可编辑数量
}
```

### 商机项目建设清单项 (ProjectBuildItem)

```tsx
interface ProjectBuildItem {
  id: string;                              // 唯一标识
  productName: string;                     // 产品项目名称
  serviceContent: string;                  // 运营服务内容
  measurementMethod: string;               // 计量方式
  quantity: number;                        // 数量
  buildStatus: string;                     // 商机项目建设状态
  scenarioId: string;                      // 关联的服务场景ID
  serviceId: string;                       // 关联的运营服务ID
}
```

## 组件架构

```
Cascade (主组件)
├── ServiceScenarioList (左侧服务场景列表)
├── ProductDetails (右侧产品详情)
│   ├── 服务场景简介
│   ├── 必选产品列表
│   └── OperationalServiceTable (运营服务明细表格)
└── ProjectBuildList (商机项目建设清单)
```

## 核心业务逻辑

### 1. 级联选择机制
- 勾选服务场景 → 必选产品自动勾选且禁用
- 取消勾选服务场景 → 必选产品恢复可编辑状态

### 2. 数据同步机制
- 服务场景选择变化 → 自动生成/移除商机项目建设清单项
- 运营服务数量编辑 → 实时同步到建设清单
- 建设清单数量编辑 → 反向同步到运营服务

### 3. 状态管理
- 使用React Hooks进行状态管理
- 通过工具函数处理复杂的数据变换
- 支持父组件监听变化事件

## 样式定制

组件使用CSS模块化设计，支持样式定制：

```css
/* 自定义主题色 */
.cascade-container {
  --primary-color: #4a90e2;
  --border-color: #e8e8e8;
  --background-color: #f5f5f5;
}
```

## 依赖要求

- React >= 16.8.0
- Ant Design >= 4.0.0
- TypeScript >= 4.0.0

## 演示

运行演示页面：

```tsx
import { CascadeDemo } from './src';

// 在你的应用中渲染
<CascadeDemo />
```

## 开发说明

### 文件结构

```
src/
├── components/          # 组件文件
│   ├── Cascade.tsx     # 主组件
│   ├── ServiceScenarioList.tsx
│   ├── ProductDetails.tsx
│   ├── OperationalServiceTable.tsx
│   └── ProjectBuildList.tsx
├── types/              # 类型定义
│   └── cascade.ts
├── utils/              # 工具函数
│   └── cascadeUtils.ts
├── data/               # 模拟数据
│   └── mockData.ts
├── pages/              # 演示页面
│   └── CascadeDemo.tsx
└── index.ts            # 入口文件
```

### 扩展开发

1. **添加新的服务场景**：在 `mockData.ts` 中添加数据
2. **自定义样式**：修改对应的 CSS 文件
3. **扩展功能**：在 `cascadeUtils.ts` 中添加工具函数
4. **类型扩展**：在 `cascade.ts` 中添加新的接口定义

## 许可证

MIT License
