import moment from 'moment';
import { AreaOption } from './interface';

// 生成年份选项
export const generateYearOptions = () => {
  const currentYear = moment().year();
  const options = [];
  
  // 生成未来两年
  options.push({
    value: (currentYear + 2).toString() + "年",
    label: (currentYear + 2).toString() + "年",
  });
  options.push({
    value: (currentYear + 1).toString() + "年",
    label: (currentYear + 1).toString() + "年",
  });

  // 生成最近3年
  for (let i = 0; i < 3; i++) {
    const year = currentYear - i;
    options.push({
      value: year.toString() + "年",
      label: year.toString() + "年",
    });
  }

  return options;
};

// 转换省份数据格式
export const transformProvinceData = (data: any): AreaOption[] => {
  if (!data?.children) return [];
  
  return data.children.map((province: any) => ({
    value: province.divisionCode,
    label: province.divisionName,
    children: province.children.map((city: any) => ({
      value: city.divisionCode,
      label: city.divisionName,
      children: city.area.map((district: any) => ({
        value: district.divisionCode,
        label: district.divisionName,
      })),
    })),
  }));
};

// Select 组件的过滤函数
export const filterOption = (input: string, option: any) => 
  (option?.label ?? "").toLowerCase().includes(input.toLowerCase()); 