import { FieldPluginProps } from "@clickpaas/cp-api";

// API 响应数据类型
export interface AreaResponse {
  code: number;
  data: {
    children?: Array<{
      children: any;
      divisionName: string;
      divisionCode: string;
      area: Array<{
        divisionName: string;
        divisionCode: string;
        area: Array<{
          divisionName: string;
          divisionCode: string;
        }>;
      }>;
    }>;
  };
}

// 配置项类型
export interface ProjectNameConfigurations {
  placeholderInput?: string;
  propName2?: number;
  propName3?: boolean;
  typeName?: string;
  maxlength?: number;
  titleName?: string;
  projectType?: string;
}

// 地区选项类型
export interface AreaOption {
  value: string;
  label: string;
  children?: AreaOption[];
}

// 级别选项类型
export interface LevelOption {
  value: string;
  label: string;
  code: number;
}

// 项目名称生成参数类型
export interface GenerateProjectNameParams {
  year: string;
  province?: { label: string; value: string };
  city?: { label: string; value: string };
  district?: { label: string; value: string };
  town?: { label: string; value: string };
  name?: string;
  type: string;
  level?: number;
  version?: { label: string; value: string };
}

// Props 类型
export interface ProjectNameProps extends FieldPluginProps {
  configurations?: ProjectNameConfigurations;
}
