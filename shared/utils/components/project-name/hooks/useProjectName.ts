import { useState, useEffect } from "react";
import moment from "moment";

export const levelOptions = [
  { value: "KQM1311", label: "省级", code: 1 },
  { value: "KQM1312", label: "市级", code: 2 },
  { value: "KQM1313", label: "区级", code: 3 },
  { value: "KQM1314", label: "县级", code: 4 },
];
export const versionOptions = [
  { label: "V1.0", value: "KQM1095" },
  { label: "V2.0", value: "KQM1096" },
  { label: "V3.0", value: "KQM1097" },
  { label: "V4.0", value: "KQM1098" },
  { label: "V5.0", value: "KQM1099" },
  { label: "V6.0", value: "KQM1100" },
];

// 定义 yearOptions 的类型
interface YearOption {
  label: string;
  value: string;
}

export const useProjectName = (
  onChange: (value: any) => void,
  typeName: string = "商机项目"
) => {
  const [typeLevel, setTypeLevel] = useState<number>(1);
  const [nameValue, setNameValue] = useState("");
  const [projectName, setProjectName] = useState<string>("");
  const [yearOptions, setYearOptions] = useState<YearOption[]>([]);
  const [selectedYear, setSelectedYear] = useState<string>(moment().format("YYYY"));
  const [level, setLevel] = useState<string>("KQM1311");
  const [version, setVersion] = useState<string>("KQM1095");




  // 初始化年份选项
  useEffect(() => {
    const currentYear = moment().year();
    const years: YearOption[] = [];
    for (let i = 0; i < 10; i++) {
      const y = (currentYear - i).toString();
      years.push({ label: y, value: y });
    }
    setYearOptions(years);
  }, []);

  // 监听 level 变化，重置相关状态
  useEffect(() => {
    // 当 level 变化时，重置所有相关状态
    setProjectName(`${selectedYear}${typeName}`);
  }, [level]);

  /**
   * 生成项目名称：年份 + areaValue + nameValue + typeName
   * @param params { year, areaValue, name, type }
   * @returns string
   */
  const generateProjectName = (params: { year: string; areaValue: string; name: string; type: string }) => {
    const { year, areaValue, name, type } = params;
    const finalName = `${year}${areaValue}${name}${type}`;
    onChange?.({ text: finalName, value: { year, areaValue, name, type } });
    return finalName;
  };

  return {
    typeLevel,
    setTypeLevel,
    nameValue,
    setNameValue,
    projectName,
    setProjectName,
    yearOptions,
    selectedYear,
    setSelectedYear,
    generateProjectName,
    versionOptions,
    version,
    setVersion,
  };
};
