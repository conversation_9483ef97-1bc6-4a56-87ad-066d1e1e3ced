import React from "react";
import { Select } from "antd";
import { AreaOption } from "../interface";
import { filterOption } from "../utils";
import { YearSelectProps } from "./interface";

export const YearSelect: React.FC<YearSelectProps> = ({
  yearOptions,
  selectedYear,
  onChange,
  onSearch,
}) => {
  return (
    <Select
      style={{ width: 120 }}
      showSearch
      placeholder="立项年份"
      optionFilterProp="children"
      onChange={onChange}
      onSearch={onSearch}
      value={selectedYear}
      filterOption={filterOption}
      options={yearOptions}
      disabled={true}
    />
  );
};
