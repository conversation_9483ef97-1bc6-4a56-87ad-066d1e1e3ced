import React from "react";
import { ProjectNameProps, LevelOption } from "../interface";

export interface BusinessProjectComponentProps {
  configurations: ProjectNameProps["configurations"];
  yearOptions: any[];
  selectedYear: string;
  typeLevel: number;
  level: string;
  provinceList: any[];
  cityList: any[];
  districtList: any[];
  townList: any[];
  selectedProvince: string | null;
  selectedCity: string | null;
  selectedDistrict: string | null;
  selectedTown: string | null;
  nameValue: string;
  projectName: string;
  levelOptions: LevelOption[];
  onChangeSelect: (value: string) => void;
  onSearch: (value: string) => void;
  onChangeProvince: (value: string) => void;
  onChangeCity: (value: string) => void;
  onChangeDistrict: (value: string) => void;
  onChangeTown: (value: string) => void;
  inputValueChange: React.ChangeEventHandler<HTMLInputElement>;
  onChangeLevel: (value: string) => void;
  titleName?:string
  versionOptions?: any[];
  version?: string;
  setVersion?: (value: string) => void;
  areaDisabled?: boolean;
}

export interface ProductProjectComponentProps {
  configurations: ProjectNameProps["configurations"];
  nameValue: string;
  projectName: string;
  levelOptions: LevelOption[];
  level: string;
  inputValueChange: React.ChangeEventHandler<HTMLInputElement>;
  onChangeLevel: (value: string) => void;
  yearOptions: any[];
  selectedYear: string;
  onChangeSelect: (value: string) => void;
  onSearch: (value: string) => void;
  versionOptions?: any[];
  version?: string;
  setVersion?: (value: string) => void;
  onChangeVersion: (value: string) => void;
}

export interface BusinessDeliveryComponentProps {
  configurations: ProjectNameProps["configurations"];
  yearOptions: any[];
  selectedYear: string;
  nameValue: string;
  projectName: string;
  levelOptions: LevelOption[];
  level: string;
  onChangeSelect: (value: string) => void;
  onSearch: (value: string) => void;
  inputValueChange: React.ChangeEventHandler<HTMLInputElement>;
  onChangeLevel: (value: string) => void;
  versionOptions?: any[];
  version?: string;
  setVersion?: (value: string) => void;
}

export interface ProjectComponentSelectorProps extends BusinessProjectComponentProps, ProductProjectComponentProps, BusinessDeliveryComponentProps {
  areaDisabled?: boolean;
  // This extends BusinessProjectComponentProps since it contains all possible props
}

export interface AreaSelectProps {
  typeLevel: number;
  provinceList: any[];
  cityList: any[];
  districtList: any[];
  townList: any[];
  selectedProvince: string | null;
  selectedCity: string | null;
  selectedDistrict: string | null;
  selectedTown: string | null;
  onProvinceChange: (value: string) => void;
  onCityChange: (value: string) => void;
  onDistrictChange: (value: string) => void;
  onTownChange: (value: string) => void;
  areaDisabled?: boolean;
}

export interface YearSelectProps {
  yearOptions: any[];
  selectedYear: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
}