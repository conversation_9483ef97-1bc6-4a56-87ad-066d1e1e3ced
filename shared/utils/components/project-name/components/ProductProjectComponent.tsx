import React from "react";
import { Form, Input, Select, Space } from "antd";
import { ProductProjectComponentProps } from "./interface";
import { YearSelect } from "./YearSelect";

export const ProductProjectComponent: React.FC<ProductProjectComponentProps> =
  ({
    configurations,
    nameValue,
    projectName,
    inputValueChange,
    yearOptions,
    selectedYear,
    onChangeSelect,
    onSearch,
    versionOptions,
    version,
    onChangeVersion,
  }) => {
    const {
      placeholderInput: placeholder = "请输入产品项目名称",
      propName2: width = 250,
      propName3: disabled,
      typeName: typeName = "产品项目",
      maxlength: maxlength = 100,
      titleName: titleName = "产品项目名称预览",
    } = configurations || {};

    return (
      <Form layout="vertical">
        <Form.Item label=""  required>
          <Space>
            <YearSelect
              yearOptions={yearOptions}
              selectedYear={selectedYear}
              onChange={onChangeSelect}
              onSearch={onSearch}
            />
            <Input
              style={{ width }}
              value={nameValue}
              onChange={inputValueChange}
              disabled={disabled}
              placeholder={placeholder}
              maxLength={maxlength}
            />
            <Select
              placeholder="请选择版本号"
              style={{ width: 150 }}
              options={versionOptions}
              onChange={onChangeVersion}
              value={version}
            />
            <span>{typeName}</span>
          </Space>
        </Form.Item>
        <Form.Item label={titleName}>
          <Input value={projectName} disabled />
        </Form.Item>
      </Form>
    );
  };
