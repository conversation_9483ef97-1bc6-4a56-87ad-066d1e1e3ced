import React from "react";
import {  Input,  Space } from "antd";
import { YearSelect } from "./YearSelect";
import { BusinessProjectComponentProps } from "./interface";

export const BusinessProjectComponent: React.FC<BusinessProjectComponentProps> =
  ({
    configurations,
    yearOptions,
    selectedYear,
    nameValue,
    onChangeSelect,
    onSearch,
    inputValueChange,
  }) => {
    const {
      placeholderInput: placeholder = "请输入项目名称",
      propName2: width = 250,
      propName3: disabled,
      typeName: typeName = "商机项目",
      maxlength: maxlength = 100,
      titleName: titleName = "商机项目名称预览",
    } = configurations || {};

    return (
      <div className="flex flex-col gap-2">
        <>
          <Space>
            <YearSelect
              yearOptions={yearOptions}
              selectedYear={selectedYear}
              onChange={onChangeSelect}
              onSearch={onSearch}
            />
            <Input disabled={true} value={"这里是传入的值"} />
            <Input
              style={{ width }}
              value={nameValue}
              onChange={inputValueChange}
              disabled={disabled}
              placeholder={placeholder}
              maxLength={maxlength}
            />
            <span>{typeName}</span>
          </Space>
        </>
      </div>
    );
  };
