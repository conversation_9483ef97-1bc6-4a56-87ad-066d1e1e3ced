import React from "react";
import { Form, Input, Select, Space } from "antd";
import { YearSelect } from "./YearSelect";
import { BusinessDeliveryComponentProps } from "./interface";

export const BusinessDeliveryComponent: React.FC<BusinessDeliveryComponentProps> = ({
  configurations,
  yearOptions,
  selectedYear,
  nameValue,
  projectName,
  levelOptions,
  level,
  onChangeSelect,
  onSearch,
  inputValueChange,
  onChangeLevel,
}) => {
  const {
    placeholderInput: placeholder = "请输入项目名称",
    propName2: width = 250,
    propName3: disabled,
    typeName: typeName = "商机项目交付小组",
    maxlength: maxlength = 100,
    titleName: titleName = "商机项目交付小组名称预览",
  } = configurations || {};

  return (
    <Form layout="vertical">
      <Form.Item label="">
        <Select
          style={{ width: 150 }}
          options={levelOptions}
          onChange={onChangeLevel}
          value={level}
        />
      </Form.Item>
      <Form.Item label="项目名称" tooltip="商机项目交付小组" required>
        <Space>
          <YearSelect
            yearOptions={yearOptions}
            selectedYear={selectedYear}
            onChange={onChangeSelect}
            onSearch={onSearch}
          />
          <Input
            style={{ width }}
            value={nameValue}
            onChange={inputValueChange}
            disabled={disabled}
            placeholder={placeholder}
            maxLength={maxlength}
          />
          <span>{typeName}</span>
        </Space>
      </Form.Item>
      <Form.Item label={titleName}>
        <Input value={projectName} disabled />
      </Form.Item>
    </Form>
  );
}; 