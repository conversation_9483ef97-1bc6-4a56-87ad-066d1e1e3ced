import React, { useEffect } from "react";
import { useProjectName } from "./hooks/useProjectName";
import { Input, Space } from "antd";
import { YearSelect } from "./components/YearSelect";
const prefixCls = "clickpaas-customize-component-1741326494767";

const ProjectName = (props: any) => {
  const { configurations, onChange } = props;
  const {
    typeLevel: typeLevel = 4,
    level: level = "KQM1311",
    typeName: typeName = "商机项目",
    titleName: titleName = "项目名称",
    projectType: projectType = "2", //1 商机项目 2 产品项目交付小组 3 商机项目交付小组
    areaValue: areaValue = "",
    splitProjectName:splitProjectName = ""
  } = configurations || {};
  console.log("configurations====", configurations);
  const {
    nameValue,
    setNameValue,
    setProjectName,
    yearOptions,
    selectedYear,
    setSelectedYear,
    generateProjectName,
    versionOptions,
    version,
  } = useProjectName(onChange, typeName);

  const handelSetProjectName = () => {
    if (projectType == "2") {
      // setTypeLevel(0);
      const versionLabel =
        versionOptions.find((v) => v.value === version)?.label || "";
      setProjectName(`${versionLabel}${typeName}`);
    } else {
      setProjectName(`${typeName}`);
    }
  };

  useEffect(() => {
    handelSetProjectName();
  }, []);

  // 处理 splitProjectName
  useEffect(() => {
    if (splitProjectName) {
      setNameValue(splitProjectName);
      setProjectName(
        generateProjectName({
          year: selectedYear || "",
          name: splitProjectName,
          type: typeName,
          areaValue: areaValue,
        })
      );
    }
  }, [splitProjectName]);

  const onChangeSelect = (value: string) => {
    setSelectedYear(value);
    let versionLabel = "";
    if (projectType === "2") {
      versionLabel =
        versionOptions.find((v) => v.value === version)?.label || "";
    }
    setProjectName(
      generateProjectName({
        year: value || "",
        areaValue: areaValue,
        name: nameValue,
        type: typeName,
      })
    );
  };

  const onSearch = (value: string) => {
    console.log("search:", value);
  };

  const inputValueChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value;
    setNameValue(value);

    let versionLabel = "";
    if (projectType === "2") {
      versionLabel =
        versionOptions.find((v) => v.value === version)?.label || "";
    }

    setProjectName(
      generateProjectName({
        year: selectedYear || "",
        name: value,
        type: typeName,
        areaValue: areaValue,
      })
    );
  };
  return (
    <div className={prefixCls}>
      <Space>
        <YearSelect
          yearOptions={yearOptions}
          selectedYear={selectedYear}
          onChange={onChangeSelect}
          onSearch={onSearch}
        />
        <Input disabled={true} value={areaValue} />
        <Input value={nameValue} onChange={inputValueChange} />
        <span>{typeName}</span>
      </Space>
    </div>
  );
};

export default ProjectName;
