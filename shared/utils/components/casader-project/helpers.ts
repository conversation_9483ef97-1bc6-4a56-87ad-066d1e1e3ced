/**
 * 格式化表格数据
 * @param data 原始数据
 * @returns 格式化后的表格数据
 */
export const formatTableData = (data: any[]) => {
  return data.map((item, index) => ({
    key: item.id || `item-${index}`,
    ...item,
  }));
};

/**
 * 格式化金额
 * @param amount 金额
 * @returns 格式化后的金额
 */
export const formatAmount = (amount: number | string): string => {
  if (!amount && amount !== 0) return "";

  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
  return numAmount.toFixed(2);
};

/**
 * 验证金额格式
 * @param amount 金额
 * @returns 是否为有效金额
 */
export const validateAmount = (amount: string): boolean => {
  const regex = /^(?!0\d)(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d{1,2})?$/;
  return regex.test(amount);
};
