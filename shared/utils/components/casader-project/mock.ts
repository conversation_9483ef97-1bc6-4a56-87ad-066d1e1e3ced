import { CascaderTableColumns } from "./interface";

/**
 * 产品类别选项模拟数据（包含子项目数据）
 */
export const mockProductCategories = [
  {
    value: "2",
    label: "测试1233",
    children: [
      {
        id: "62",
        name: "测试1233.5",
        category: "测试1233",
        constructionTime: "2023-05-10",
        vendor: "供应商F",
        amount: "110",
        children: [
          {
            id: "621",
            name: "测试1233.5.1",
            category: "测试1233",
            constructionTime: "2023-05-10",
            vendor: "供应商F",
            amount: "110",
          },
          {
            id: "622",
            name: "测试1233.5.2",
            category: "测试1233",
            constructionTime: "2023-05-12",
            vendor: "供应商F",
            amount: "120",
          },
        ],
      },
      {
        id: "71",
        name: "政务知识库管理平台V2.0",
        category: "测试1233",
        constructionTime: "2023-06-18",
        vendor: "供应商G",
        amount: "145",
        children: [
          {
            id: "711",
            name: "政务知识库管理平台V2.0.1",
            category: "测试1233",
            constructionTime: "2023-06-18",
            vendor: "供应商G",
            amount: "145",
          },
          {
            id: "712",
            name: "政务知识库管理平台V2.0.2",
            category: "测试1233",
            constructionTime: "2023-06-20",
            vendor: "供应商G",
            amount: "155",
          },
        ],
      },
    ],
  },
  {
    value: "AI+智能服务中枢",
    label: "AI+智能服务中枢",
    children: [
      {
        id: "28",
        name: "AI智能分析平台V1.0",
        category: "AI+智能服务中枢",
        constructionTime: "2023-07-22",
        vendor: "供应商H",
        amount: "180",
        children: [
          {
            id: "281",
            name: "AI数据处理模块",
            category: "AI+智能服务中枢",
            constructionTime: "2023-07-22",
            vendor: "供应商H",
            amount: "180",
          },
          {
            id: "282",
            name: "AI模型训练模块",
            category: "AI+智能服务中枢",
            constructionTime: "2023-07-25",
            vendor: "供应商H",
            amount: "190",
          },
        ],
      },
      {
        id: "93",
        name: "智能服务中枢系统V2.1",
        category: "AI+智能服务中枢",
        constructionTime: "2023-08-15",
        vendor: "供应商I",
        amount: "220",
        children: [
          {
            id: "931",
            name: "智能服务基础系统",
            category: "AI+智能服务中枢",
            constructionTime: "2023-08-15",
            vendor: "供应商I",
            amount: "220",
          },
          {
            id: "932",
            name: "智能服务扩展系统",
            category: "AI+智能服务中枢",
            constructionTime: "2023-08-18",
            vendor: "供应商I",
            amount: "230",
          },
        ],
      },
    ],
  },
];

// 1级
export const mockProductCategories2 = [
  {
    value: "2",
    label: "测试1233",
    children: [
      {
        id: "62",
        name: "测试1233.5",
        productCategory: "测试1233",
        constructionTime: "2023-05-10",
        vendor: "供应商F",
        productProjectName: "测试1233.5",
        amount: "110",
      },
      {
        id: "71",
        name: "政务知识库管理平台V2.0",
        productCategory: "测试1233",
        constructionTime: "2023-06-18",
        vendor: "供应商G",
        productProjectName: "政务知识库管理平台V2.0",
        amount: "145",
      },
    ],
  },
];
export const mockProductCategories3 = [
  {
    value: "2",
    label: "测试1233",
    productCategory: "测试1233",
    id: "2",
  },
  {
    value: "3",
    label: "AI+智能服务中枢",
    productCategory: "AI+智能服务中枢",
    id: "3",
  },
];

/**
 * 回显示例数据 - 图片中已经勾选的产品项目
 */
export const mockSelectedProjects: any[] = [
  // {
  //   id: "281",
  //   name: "AI数据处理模块",
  //   category: "AI+智能服务中枢",
  //   constructionTime: "2023-07-22",
  //   vendor: "供应商H",
  //   amount: "180",
  // },
  // {
  //   id: "282",
  //   name: "AI模型训练模块",
  //   category: "AI+智能服务中枢",
  //   constructionTime: "2023-07-25",
  //   vendor: "供应商H",
  //   amount: "190",
  // },
];

export const tableColumns: CascaderTableColumns = [
  {
    title: "产品类别",
    dataIndex: "productCategory",
    key: "productCategory",
    width: "25%",
  },
  {
    title: "产品项目名称",
    dataIndex: "productProjectName",
    key: "productProjectName",
    width: "25%",
  },
  {
    title: "建设时间",
    dataIndex: "constructionTime",
    key: "constructionTime",
    width: "15%",
    editable: true,
  },
  {
    title: "厂商",
    dataIndex: "vendor",
    key: "vendor",
    width: "15%",
    editable: true,
  },
  {
    title: "金额（单位：万元）",
    dataIndex: "amount",
    key: "amount",
    width: "15%",
    editable: true,
  },
];
