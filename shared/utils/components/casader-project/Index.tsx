import React, { useState, useEffect, useRef } from "react";
import { Table, Button, Checkbox, Modal, Empty, Input } from "antd";
import { RightOutlined } from "@ant-design/icons";
import type { CheckboxChangeEvent } from "antd/lib/checkbox";
import { formatAmount } from "./helpers";
import { CascaderProjectProps } from "./interface";

interface ProjectItem {
  id: string;
  name: string;
  category: string;
  constructionTime?: string;
  vendor?: string;
  amount?: string;
  children?: ProjectItem[];
  [key: string]: any;
}

const CascaderProject: React.FC<CascaderProjectProps> = ({
  productCategories = [],
  value = [],
  onChange,
  onDelete,
  height = "auto",
  columnsConfig = [],
  tableColumns = [],
}) => {
  // 创建层级状态
  const [selections, setSelections] = useState<{
    [key: number]: { selected: string | null; options: ProjectItem[] };
  }>({
    0: { selected: null, options: productCategories },
  });

  // 将单一字符串数组改为嵌套数组，每个路径为一个数组，格式为 [["1级id","2级Id","3级id"]]
  const [checkedProjects, setCheckedProjects] = useState<(string | null)[][]>(
    []
  );

  // 半选状态项目ID集合
  const [indeterminateProjects, setIndeterminateProjects] = useState<string[]>(
    []
  );

  // 用于存储表格数据，与选中项分开管理
  const [tableData, setTableData] = useState<any[]>([]);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<any>(null);
  const isInitializedRef = useRef(false);

  // 最大层级
  const maxLevel = columnsConfig.length || 1; // 确保至少为1

  // 添加调试日志，查看组件初始化时的关键变量
  console.log("CascaderProject 初始化:", {
    maxLevel,
    productCategoriesLength: productCategories.length,
    valueLength: value.length,
    isInitialized: isInitializedRef.current,
  });

  // 表格列定义根据 tableColumns配制 如果editable是true那么就是一个输入框，输入框更改的时候要调用onChange并修改tableData在父组件能够获取到

  const actionItem = {
    title: "操作",
    key: "action",
    render: (_: any, record: any) => (
      <Button type="link" danger onClick={() => handleShowDeleteModal(record)}>
        删除
      </Button>
    ),
  };
  // 如果tableColumns中至少有一个editable为true就添加actionItem
  const columns = [
    ...tableColumns.map((column) => ({
      ...column,
      render: (text: string, record: any) =>
        column.editable ? (
          <Input
            value={text}
            onChange={(e) => handleInputChange(e, record, column)}
          />
        ) : (
          text
        ),
    })),
    ...(tableColumns.some((column) => column.editable) ? [actionItem] : []),
  ];
  // 初始化: 更新已有的选中项并设置默认展开第一个元素
  useEffect(() => {
    console.log("useEffect 触发:", {
      maxLevel,
      valueLength: value?.length,
      isInitialized: isInitializedRef.current,
      productCategoriesLength: productCategories?.length,
    });

    // 单级模式下，不使用 useEffect 更新选中状态，避免清空表格
    if (maxLevel === 1 && isInitializedRef.current) {
      return;
    }

    // 已有选中项的初始化
    if (value && value.length > 0 && !isInitializedRef.current) {
      // 将回显数据转换为路径数组格式
      const newCheckedProjects: (string | null)[][] = [];
      const newTableData: any[] = [];

      // 处理回显数据
      value.forEach((item: any) => {
        if (item) {
          // 构建项目路径，这里需要根据实际业务逻辑和数据结构来实现
          // 假设我们可以通过遍历productCategories找到完整路径
          const path = findProjectPathById(productCategories, item.id);
          if (path && path.length > 0) {
            newCheckedProjects.push(path);
            // 添加到表格数据 需要更具 tableColumns的dataIndex作为key 来添加到表格
            const newRow = {
              key: item.id,
              ...tableColumns.reduce((acc: any, column) => {
                acc[column.dataIndex] = item[column.dataIndex];
                return acc;
              }, {}),
              originalData: item,
            };
            console.log("添加到表格22222222222", newRow);
            newTableData.push(newRow);
          }
        }
      });

      setCheckedProjects(newCheckedProjects);
      console.log("newCheckedProjects=---------33333333333", newTableData);
      setTableData(newTableData);

      // 更新半选状态
      updateIndeterminateState(newCheckedProjects);

      isInitializedRef.current = true;
    } else if (isInitializedRef.current && maxLevel !== 1) {
      // 只在非单级模式下且已初始化过的情况下更新数据
      // 单级模式下的更新在 handleItemCheck 中直接处理，避免在这里重置表格数据
    } else if (!isInitializedRef.current) {
      // 即使没有初始值，也标记为已初始化，避免后续不必要的触发
      console.log("没有初始值，但标记为已初始化");
      isInitializedRef.current = true;
    }

    // 默认展开第一个元素（如果没有已选中的项）
    if (productCategories.length > 0 && Object.keys(selections).length <= 1) {
      const firstItem = productCategories[0];
      if (firstItem) {
        // 模拟点击第一个元素
        handleLevelSelect(0, firstItem);
      }
    }
  }, [productCategories, value, maxLevel, tableColumns]);

  // 查找项目的完整路径
  const findProjectPathById = (
    categories: any[],
    targetId: string,
    currentPath: (string | null)[] = []
  ): (string | null)[] | null => {
    for (const category of categories) {
      // 一级分类
      const level1Path = [...currentPath, category.value];

      if (category.children) {
        for (let i = 0; i < category.children.length; i++) {
          const project: any = category.children[i];
          // 二级项目
          const level2Path = [...level1Path, project.id];

          if (project.id === targetId) {
            return level2Path;
          }

          if (project.children) {
            for (let j = 0; j < project.children.length; j++) {
              const module: any = project.children[j];
              // 三级模块
              const level3Path = [...level2Path, module.id];

              if (module.id === targetId) {
                return level3Path;
              }
            }
          }
        }
      }
    }

    return null;
  };

  // 判断一个ID是否被选中(完全匹配路径)
  const isProjectChecked = (id: string): boolean => {
    // 检查表格数据中是否存在该ID（更直接的方式检查选中状态）
    if (maxLevel === 1) {
      return tableData.some((row) => row.key === id);
    }
    // 多级模式下使用路径检查
    return checkedProjects.some((path) => path.includes(id));
  };

  // 判断一个ID是否处于半选状态(作为父节点且部分子节点被选中)
  const isProjectIndeterminate = (id: string): boolean => {
    return indeterminateProjects.includes(id);
  };

  // 选择某一层级的选项
  const handleLevelSelect = (level: number, item: any) => {
    // 如果只有一级，则不需要额外处理
    if (maxLevel === 1) {
      return;
    }

    // 创建新的selections对象
    console.log("handleLevelSelect", level, item);
    const newSelections = { ...selections };

    // 设置当前层级的选中项
    newSelections[level] = {
      ...newSelections[level],
      selected: item.value || item.id,
    };

    // 如果有子项，设置下一层级的选项
    if (item.children && item.children.length > 0) {
      newSelections[level + 1] = {
        selected: null,
        options: item.children,
      };

      // 清除所有后续层级
      for (let i = level + 2; i < maxLevel; i++) {
        if (newSelections[i]) {
          delete newSelections[i];
        }
      }
    } else {
      // 如果没有子项，清除所有后续层级
      for (let i = level + 1; i < maxLevel; i++) {
        if (newSelections[i]) {
          delete newSelections[i];
        }
      }
    }

    setSelections(newSelections);
  };

  // 判断是否为最后一级
  const isLastLevel = (level: number, item: ProjectItem): boolean => {
    return (
      level === maxLevel - 1 || !item.children || item.children.length === 0
    );
  };

  // 获取节点的所有子节点ID（递归）
  const getAllChildrenIds = (item: ProjectItem): string[] => {
    if (!item.children || item.children.length === 0) {
      return [];
    }

    let ids: string[] = [];
    item.children.forEach((child: ProjectItem) => {
      ids.push(child.id);
      const childrenIds = getAllChildrenIds(child);
      if (childrenIds.length > 0) {
        ids = [...ids, ...childrenIds];
      }
    });

    return ids;
  };

  // 获取节点的直接子节点ID（非递归）
  const getDirectChildrenIds = (item: ProjectItem): string[] => {
    if (!item.children || item.children.length === 0) {
      return [];
    }

    return item.children.map((child: ProjectItem) => child.id);
  };

  // 构建从ID到路径的映射关系
  const buildIdToPathMapping = (): Map<string, (string | null)[]> => {
    const mapping = new Map<string, (string | null)[]>();

    const traverseTree = (
      items: any[],
      currentPath: (string | null)[] = []
    ) => {
      items.forEach((item: any) => {
        const itemValue = item.value || item.id;
        if (itemValue) {
          const path = [...currentPath, itemValue];
          mapping.set(itemValue, path);

          if (item.children && item.children.length > 0) {
            traverseTree(item.children, path);
          }
        }
      });
    };

    traverseTree(productCategories);
    return mapping;
  };

  // 更新半选状态
  const updateIndeterminateState = (projects: (string | null)[][]) => {
    // 如果只有一级，无需计算半选状态
    if (maxLevel === 1) {
      setIndeterminateProjects([]);
      return;
    }

    const newIndeterminate: string[] = [];
    const idToPathMap = buildIdToPathMapping();
    const allPaths = [...projects];

    // 为每个ID创建子节点集合
    const childrenMap = new Map<string, Set<string>>();
    const checkedItemsSet = new Set<string>();

    // 添加所有选中的ID到Set中
    allPaths.forEach((path: (string | null)[]) => {
      path.forEach((id: string | null) => {
        if (id !== null) {
          checkedItemsSet.add(id);
        }
      });
    });

    // 构建父子关系映射
    productCategories.forEach((category: any) => {
      if (category.children) {
        const categoryId = category.value;
        if (!childrenMap.has(categoryId)) {
          childrenMap.set(categoryId, new Set<string>());
        }

        category.children.forEach(
          (project: { id: string; children?: any[] }) => {
            childrenMap.get(categoryId)?.add(project.id);

            if (project.children) {
              if (!childrenMap.has(project.id)) {
                childrenMap.set(project.id, new Set<string>());
              }

              project.children.forEach((module: { id: string }) => {
                childrenMap.get(project.id)?.add(module.id);
              });
            }
          }
        );
      }
    });

    // 检查每个有子节点的ID
    childrenMap.forEach((children: Set<string>, parentId: string) => {
      if (children.size > 0) {
        // 计算选中的子节点数量
        let checkedCount = 0;
        children.forEach((childId: string) => {
          if (checkedItemsSet.has(childId)) {
            checkedCount++;
          }
        });

        // 如果部分子节点被选中但不是全部，则为半选状态
        if (checkedCount > 0 && checkedCount < children.size) {
          newIndeterminate.push(parentId);
        }
      }
    });

    setIndeterminateProjects(newIndeterminate);
  };

  // Checkbox变更处理
  const handleItemCheck = (
    e: CheckboxChangeEvent,
    item: ProjectItem,
    level: number
  ) => {
    e.stopPropagation();
    const { checked } = e.target;
    console.log("handleItemCheck 触发:", {
      itemId: item.id,
      checked,
      level,
      maxLevel,
      tableDataLength: tableData.length,
    });

    // 构建当前项的路径
    let path: (string | null)[] = [];

    // 如果只有一级，简化路径构建，只包含当前ID
    if (maxLevel === 1) {
      path = [item.id];
    } else {
      // 添加前面几级的路径
      for (let i = 0; i < level; i++) {
        const selectedValue = selections[i]?.selected;
        path.push(selectedValue);
      }

      // 添加当前级别
      path.push(item.id);
    }

    // 创建新的选中项数组
    let newCheckedProjects = [...checkedProjects];
    let newTableData = [...tableData];

    // 当勾选复选框时，如果有子节点且不是单级模式，自动展示子节点
    if (checked && item.children && item.children.length > 0 && maxLevel > 1) {
      handleLevelSelect(level, item);
    }

    if (checked) {
      // 添加当前路径
      const pathExists = newCheckedProjects.some(
        (existingPath) =>
          existingPath.length === path.length &&
          existingPath.every((val, idx) => val === path[idx])
      );

      if (!pathExists) {
        newCheckedProjects.push([...path]);
      }

      // 当只有一级时，直接添加到表格，不需要检查是否是最后一级
      const shouldAddToTable = maxLevel === 1 || isLastLevel(level, item);

      // 如果是最后一级或叶子节点或只有一级，添加到表格
      if (shouldAddToTable) {
        // 检查表格中是否已存在
        const existsInTable = tableData.some((row) => row.key === item.id);

        if (!existsInTable) {
          // 添加到表格 需要更具 tableColumns的dataIndex作为key 来添加到表格
          const newRow = {
            key: item.id,
            ...tableColumns.reduce((acc: any, column) => {
              acc[column.dataIndex] = item[column.dataIndex];
              return acc;
            }, {}),
            originalData: item,
          };
          console.log("添加到表格11111111", newRow);

          newTableData.push(newRow);

          // 在单级模式下立即更新表格数据
          if (maxLevel === 1) {
            console.log("单级模式立即更新表格数据:", newTableData);
            // 直接设置表格数据
            setTableData([...newTableData]);

            // 确保更新状态对象，强制触发刷新
            setCheckedProjects([...newCheckedProjects]);

            // 立即调用外部 onChange
            if (onChange) {
              onChange(newTableData.map((row) => row.originalData));
            }
          }
        }
      }

      // 如果有子节点且不是单级模式，递归添加所有子节点路径
      if (item.children && item.children.length > 0 && maxLevel > 1) {
        item.children.forEach((child: ProjectItem) => {
          const childPath = [...path.slice(0, level + 1)];
          childPath.push(child.id);

          // 添加到选中路径
          const childPathExists = newCheckedProjects.some(
            (existingPath) =>
              existingPath.length === childPath.length &&
              existingPath.every((val, idx) => val === childPath[idx])
          );

          if (!childPathExists) {
            newCheckedProjects.push([...childPath]);
          }

          // 如果是最后一级，添加到表格
          if (isLastLevel(level + 1, child)) {
            // 检查表格中是否已存在
            const childExistsInTable = tableData.some(
              (row) => row.key === child.id
            );

            if (!childExistsInTable) {
              // 添加到表格 需要更具 tableColumns的dataIndex作为key 来添加到表格
              const childRow = {
                key: child.id,
                ...tableColumns.reduce((acc: any, column) => {
                  acc[column.dataIndex] = child[column.dataIndex];
                  return acc;
                }, {}),
                originalData: child,
              };

              newTableData.push(childRow);
            }
          }

          // 递归处理更深层级的子节点
          if (child.children && child.children.length > 0) {
            // 这里可以递归处理更深层级，如果需要的话
          }
        });
      }
    } else {
      // 取消选中当前路径
      newCheckedProjects = newCheckedProjects.filter(
        (existingPath) =>
          !(
            existingPath.length === path.length &&
            existingPath.every((val, idx) => val === path[idx])
          )
      );

      // 移除以当前路径为前缀的所有路径
      newCheckedProjects = newCheckedProjects.filter((existingPath) => {
        if (existingPath.length <= path.length) return true;

        // 检查现有路径是否以当前路径为前缀
        for (let i = 0; i < path.length; i++) {
          if (existingPath[i] !== path[i]) return true;
        }

        return false;
      });

      // 从表格中移除当前项及其子项
      const idsToRemove = [item.id, ...getAllChildrenIds(item)];
      newTableData = newTableData.filter(
        (row) => !idsToRemove.includes(row.key)
      );

      // 在单级模式下立即更新表格数据
      if (maxLevel === 1) {
        console.log("单级模式取消选中立即更新表格数据:", newTableData);
        // 直接设置表格数据
        setTableData([...newTableData]);

        // 确保更新状态对象，强制触发刷新
        setCheckedProjects([...newCheckedProjects]);

        // 立即调用外部 onChange
        if (onChange) {
          onChange(newTableData.map((row) => row.originalData));
        }
      }
    }

    // 更新状态
    setCheckedProjects(newCheckedProjects);
    console.log("newCheckedProject=---------", newTableData);

    // 在非单级模式下才进行这些更新
    // 单级模式的更新已经在选中/取消选中时直接进行了
    if (maxLevel > 1) {
      setTableData(newTableData);

      // 更新半选状态
      updateIndeterminateState(newCheckedProjects);

      // 处理表格数据和返回值
      const newValue = newTableData.map((row) => row.originalData);

      // 调用外部onChange
      if (onChange) {
        onChange(newValue);
      }
    }
  };

  // 显示删除确认对话框
  const handleShowDeleteModal = (record: any) => {
    setRecordToDelete(record);
    setDeleteModalVisible(true);
  };

  // 确认删除
  const handleConfirmDelete = () => {
    if (recordToDelete) {
      // 从表格数据移除
      const newTableData = tableData.filter(
        (item) => item.key !== recordToDelete.key
      );
      console.log("newTableData=---------55555555555", newTableData);
      setTableData(newTableData);

      // 从选中列表移除相关路径
      const newCheckedProjects = checkedProjects.filter(
        (path) => !path.includes(recordToDelete.key)
      );
      setCheckedProjects(newCheckedProjects);

      // 更新半选状态
      updateIndeterminateState(newCheckedProjects);

      // 更新value并调用外部onChange
      const newValue = newTableData.map((row) => row.originalData);

      if (onChange) {
        // 将删除后的值传递给父组件
        onChange(newValue);
      }

      // 调用外部onDelete
      if (onDelete) {
        onDelete(recordToDelete.originalData || recordToDelete);
      }

      // 关闭弹窗
      setDeleteModalVisible(false);
      setRecordToDelete(null);
    }
  };

  // 取消删除
  const handleCancelDelete = () => {
    setDeleteModalVisible(false);
    setRecordToDelete(null);
  };

  // 渲染级联选择区域
  const renderCascadeColumns = () => {
    const columns: React.ReactNode[] = [];

    // 根据配置渲染各列
    for (let i = 0; i < maxLevel; i++) {
      const currentLevel = selections[i];
      const columnTitle = columnsConfig[i]?.label || `级别 ${i + 1}`;

      columns.push(
        <div key={i} className="w-1/3 rounded border">
          <div className="p-3 bg-gray-50 border-b">
            <h3 className="text-base text-blue-500">{columnTitle}</h3>
          </div>
          <div className="overflow-y-auto p-3" style={{ maxHeight: "250px" }}>
            {currentLevel?.options && currentLevel.options.length > 0 ? (
              <div className="flex flex-col gap-3">
                {currentLevel.options.map((item) => (
                  <div
                    key={item.id || item.value}
                    className={`flex items-center cursor-pointer ${
                      currentLevel.selected === (item.id || item.value)
                        ? "text-blue-500"
                        : ""
                    }`}
                    onClick={() => handleLevelSelect(i, item)}
                  >
                    {(i > 0 || maxLevel === 1) && ( // 当columnsConfig只有1个时或非第一级时显示复选框
                      <Checkbox
                        checked={isProjectChecked(item.id)}
                        indeterminate={isProjectIndeterminate(item.id)}
                        onChange={(e) => handleItemCheck(e, item, i)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                    <span className={i === 0 && maxLevel !== 1 ? "" : "ml-2"}>
                      {item.name || item.label}
                    </span>
                    {currentLevel.selected === (item.id || item.value) &&
                      item.children &&
                      item.children.length > 0 && (
                        <RightOutlined className="ml-auto text-blue-500" />
                      )}
                  </div>
                ))}
              </div>
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无数据"
              />
            )}
          </div>
        </div>
      );
    }

    return <div className="flex gap-4 mb-6">{columns}</div>;
  };

  // 处理输入框值变化
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    record: any,
    column: any
  ) => {
    const { value } = e.target;
    const newValue = value;

    // 更新表格数据
    const newTableData = tableData.map((item) => {
      if (item.key === record.key) {
        const updatedItem = {
          ...item,
          [column.dataIndex]: newValue,
          originalData: {
            ...item.originalData,
            [column.dataIndex === "productCategory"
              ? "category"
              : column.dataIndex === "productProjectName"
              ? "name"
              : column.dataIndex]: newValue,
          },
        };
        return updatedItem;
      }
      return item;
    });
    console.log("newTableData=---------22222222222", newTableData);
    setTableData(newTableData);

    // 调用外部onChange以通知父组件数据变更
    if (onChange) {
      onChange(newTableData.map((row) => row.originalData));
    }
  };

  return (
    <div className="flex flex-col" style={{ height, overflow: "auto" }}>
      <div className="p-4 mb-6 bg-white rounded shadow-sm">
        <h2 className="mb-4 text-lg font-medium">选择产品</h2>
        {renderCascadeColumns()}
      </div>

      <div className="flex-grow p-4 bg-white rounded shadow-sm">
        <h2 className="mb-4 text-lg font-medium">已选择项目</h2>
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={false}
          rowKey="key"
          locale={{
            emptyText: <Empty description="暂无数据，请在上方选择产品" />,
          }}
        />
      </div>

      {/* 删除确认对话框 */}
      <Modal
        title="确认删除"
        visible={deleteModalVisible}
        onOk={handleConfirmDelete}
        onCancel={handleCancelDelete}
        okText="确认"
        cancelText="取消"
      >
        <p>
          确定要删除 <strong>{recordToDelete?.productProjectName}</strong>{" "}
          吗？此操作不可撤销。
        </p>
      </Modal>
    </div>
  );
};

export default CascaderProject;
