
export interface CascaderProjectProps {
    /**
     * 产品类别选项（包含子项目数据）
     */
    productCategories?: any[];
  
    /**
     * 当前选中的项目值，用于回显
     */
    value?: any[];
  
    /**
     * 项目选择变化时的回调
     */
    onChange?: (value: any[]) => void;
  
    /**
     * 删除记录的回调函数
     */
    onDelete?: (record: any) => void;
    tableColumns?: CascaderTableColumns;
    /**
     * 组件高度
     */
    height?: number | string;
    columnsConfig?: {
      label: string;
      value: string;
    }[];
  }
export interface CascaderTableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width: string;
  editable?: boolean;
}

export type CascaderTableColumns = CascaderTableColumn[];