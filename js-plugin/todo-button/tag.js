//  这是一个js脚本

(function () {
  console.log("脚本加载了================");
  // ===================== ApprovalListManager =====================
  class ApprovalListManager {
    constructor() {
      this.listKey = "approval_list";
      this.indexKey = "approval_index";
      this.fetchUrl =
        "https://pms.tongbaninfo.com:8888/app/api/bpm/workItem/todoList";
      this.fetchOptions = {
        headers: {
          _lang: "zh_CN",
          accept: "application/json, text/plain, */*",
          "accept-language": "zh-CN,zh;q=0.9",
          "content-type": "application/json",
          "sec-ch-ua":
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"macOS"',
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
        },
        body: JSON.stringify({
          pageNo: 1,
          pageSize: 50,
          param: {
            workTypes: [
              "TRANSFER",
              "BEFORE_APPEND",
              "AFTER_APPEND",
              "SELF",
              "OPINION",
              "AGENT",
              "ORIGINATOR_SUBMIT",
            ],
          },
        }),
        method: "POST",
        mode: "cors",
        credentials: "include",
      };
    }

    getList() {
      try {
        return JSON.parse(localStorage.getItem(this.listKey)) || [];
      } catch {
        return [];
      }
    }
    setList(list) {
      localStorage.setItem(this.listKey, JSON.stringify(list));
    }
    getIndex() {
      return parseInt(localStorage.getItem(this.indexKey) || "0", 10);
    }
    setIndex(idx) {
      localStorage.setItem(this.indexKey, String(idx));
    }
    async fetchAndStoreList() {
      try {
        console.log("开始获取待办列表...");
        const res = await fetch(this.fetchUrl, this.fetchOptions);
        const data = await res.json();
        if (data && data.data && Array.isArray(data.data.body)) {
          let list = data.data.body.map((item) => ({
            id: item.data.businessId.value,
            name: item.data.businessId.text || item.data.businessId.value,
            mainStandardBOId: item.data.metadataId?.value,
            tenantId: item.data.tenantId?.value,
            companyId: item.data.companyId?.value,
            processDefVersionId: item.data.processDefVersionId?.value,
            nodeDefId: item.data.nodeDefId?.value,
            standardCollectionId: item.data.metadataId?.value,
            processIntsId: item.data.processIntsId?.value,
            processInstId: item.data.processInstId?.value,
          }));
          
          // 获取当前URL中的ID并重新排序，使当前ID始终在第一位
          const currentId = this.getCurrentIdFromUrl();
          if (currentId) {
            const currentIndex = list.findIndex(item => item.id === currentId);
            if (currentIndex > 0) {
              // 将当前项移到第一位
              const currentItem = list.splice(currentIndex, 1)[0];
              list.unshift(currentItem);
              console.log("已将当前ID移到列表第一位:", currentId);
            }
          }
          
          this.setList(list);
          this.setIndex(1);
          console.log("待办列表获取成功，共", list.length, "条");
        } else {
          console.warn("待办列表数据格式异常", data);
        }
      } catch (e) {
        console.error("获取待办列表失败", e);
      }
    }
    async checkAndFetchList() {
      let idxRaw = localStorage.getItem(this.indexKey);
      let idx = idxRaw === null ? null : parseInt(idxRaw, 10);
      if (idx === null || idx === 8) {
        await this.fetchAndStoreList();
      }
    }
    increaseIndex() {
      let idx = this.getIndex();
      this.setIndex(idx + 1);
    }
    removeId(id) {
      const list = this.getList().filter((item) => item.id !== id);
      this.setList(list);
    }
    getNextId(currentId) {
      const list = this.getList();
      if (list.length === 0) return null;

      const currentIndex = list.findIndex((item) => item.id === currentId);
      if (currentIndex === -1) {
        // 如果当前id不在列表中，返回第一个
        return list[0].id;
      }

      // 获取下一个索引，如果是最后一个就回到第0个
      const nextIndex = (currentIndex + 1) % list.length;
      return list[nextIndex].id;
    }
    getCurrentIdFromUrl() {
      const match = window.location.href.match(/id=([^&]+)/);
      return match ? match[1] : null;
    }
  }

  // ===================== 按钮监听与路由监听 =====================
  let approveBtn = null;
  let approveBtnHandler = null;
  let routeObserver = null;
  let cleanupRouteChange = null;
  const approvalManager = new ApprovalListManager();

  function removeApproveListener() {
    if (approveBtn && approveBtnHandler) {
      approveBtn.removeEventListener("click", approveBtnHandler);
    }
    approveBtn = null;
    approveBtnHandler = null;
  }

  function insertNextButton(retryCount = 0) {
    console.log("插入========", retryCount);
    
    // 检查待办列表长度，如果只有1条数据则不插入按钮
    const list = approvalManager.getList();
    if (list.length <= 1) {
      console.log("待办列表只有", list.length, "条数据，不插入【去下一个】按钮");
      // 如果已经存在按钮，则移除它
      removeNextButton();
      return;
    }
    
    const container = document.querySelector(
      ".clickpaas-pages-standard-object-approval-view-approval-flow-components-approval-handle-bar-approval-handle-bar-approvalHandleBar  .buttons"
    );
    if (!container) {
      if (retryCount < 10) {
        console.log("容器未找到，500ms后重试...", retryCount + 1);
        setTimeout(() => insertNextButton(retryCount + 1), 500);
      } else {
        console.warn("容器未找到，已达到最大重试次数");
      }
      return;
    }

    if (container.querySelector(".custom-next-btn")) return;

    const nextBtn = document.createElement("button");
    nextBtn.type = "button";
    nextBtn.className = "hekit-ui-btn hekit-ui-btn-primary custom-next-btn";
    nextBtn.style.marginLeft = "10px";
    nextBtn.innerHTML = "<span id='next-btn'>去下一个</span>";
    nextBtn.onclick = handleNextApprove;
    container.appendChild(nextBtn);
    console.log("【去下一个】按钮插入成功");
  }

  let checkNextBtnTimer = null;

  function removeNextButton() {
    const existingBtn = document.querySelector(".custom-next-btn");
    if (existingBtn) {
      existingBtn.remove();
      console.log("【去下一个】按钮已销毁");
    }
    // 清除定时器
    if (checkNextBtnTimer) {
      clearTimeout(checkNextBtnTimer);
      checkNextBtnTimer = null;
    }
  }

  function checkAndInsertNextButton(retryCount = 0) {
    if (!window.location.href.includes("/approval/view")) return;
    console.log("检查========", retryCount);
    
    // 检查待办列表长度
    const list = approvalManager.getList();
    if (list.length <= 1) {
      console.log("待办列表只有", list.length, "条数据，移除【去下一个】按钮");
      removeNextButton();
      return;
    }
    
    const existingBtn = document.getElementById("next-btn");
    if (existingBtn) {
      console.log("【去下一个】按钮已存在，无需插入");
      return;
    }

    if (retryCount < 10) {
      console.log("检查并尝试插入【去下一个】按钮...", retryCount + 1);
      insertNextButton();
      const container = document.querySelector(
        ".clickpaas-pages-standard-object-approval-view-approval-flow-components-approval-handle-bar-approval-handle-bar-approvalHandleBar  .buttons"
      );
      console.log("container", container);
      checkNextBtnTimer = setTimeout(
        () => checkAndInsertNextButton(retryCount + 1),
        2000
      );
    } else {
      console.warn("【去下一个】按钮插入尝试已达到最大次数");
    }
  }

  function listenApproveButton(retryCount = 0) {
    removeApproveListener();
    if (!window.location.href.includes("/approval/view")) return;

    approveBtn = document.querySelector(".buttons");
    console.log("buttons容器", approveBtn);

    if (!approveBtn) {
      if (retryCount < 10) {
        console.log("同意按钮未找到，2秒后重试...", retryCount + 1);
        setTimeout(() => listenApproveButton(retryCount + 1), 2000);
      } else {
        console.warn("同意按钮未找到，已达到最大重试次数，放弃监听");
      }
      return;
    }

    // 检查是否需要获取todoList
    approvalManager.checkAndFetchList();
    // 开始检查并插入"去下一个"按钮
    checkAndInsertNextButton();
  }

  // ===================== "去下一个"按钮逻辑 =====================

  function replaceUrlParams(url, currentItem, nextItem) {
    let newUrl = url;

    console.log("当前项目数据:", nextItem);

    // 替换所有相关参数
    const paramMap = {
      id: nextItem.id,
      processInstId: nextItem.processInstId,
      processIntsId: nextItem.processInstId, // 修正：使用正确的字段
      standardCollectionId: nextItem.standardCollectionId,
      nodeDefId: nextItem.nodeDefId,
      processDefVersionId: nextItem.processDefVersionId,
      companyId: nextItem.companyId,
      tenantId: nextItem.tenantId,
      mainStandardBOId: nextItem.mainStandardBOId,
    };

    console.log("参数映射:", paramMap);

    // 逐个替换参数
    Object.entries(paramMap).forEach(([param, value]) => {
      if (value) {
        const regex = new RegExp(`(${param}=)[^&]*`, "g");
        const oldUrl = newUrl;
        newUrl = newUrl.replace(regex, `$1${value}`);
        if (oldUrl !== newUrl) {
          console.log(`替换参数 ${param}: ${value}`);
        }
      } else {
        console.warn(`参数 ${param} 的值为空:`, value);
      }
    });

    console.log("原URL:", url);
    console.log("新URL:", newUrl);

    return newUrl;
  }

  function handleNextApprove() {
    const currentId = approvalManager.getCurrentIdFromUrl();
    if (!currentId) {
      console.warn("未能从URL中获取当前id");
      return;
    }

    const list = approvalManager.getList();
    const currentIndex = list.findIndex((item) => item.id === currentId);
    const nextIndex =
      currentIndex === -1 ? 0 : (currentIndex + 1) % list.length;
    const nextItem = list[nextIndex];

    if (!nextItem) {
      alert("没有更多待办了！");
      return;
    }

    let newUrl = replaceUrlParams(window.location.href, null, nextItem);

    // 添加时间戳参数强制绕过浏览器缓存
    const timestamp = Date.now();
    const separator = newUrl.includes("?") ? "&" : "?";
    newUrl = `${newUrl}${separator}_t=${timestamp}`;

    approvalManager.increaseIndex(); // 每次点击"去下一个"时approval_index +1
    console.log("跳转到下一个:", nextItem.id, "URL:", newUrl);
    window.location.href = newUrl;
  }

  // ===================== 路由监听与内存清理 =====================
  function listenRouteChange(callback) {
    let oldHref = location.href;
    const body = document.querySelector("body");

    if (routeObserver) routeObserver.disconnect();
    routeObserver = new MutationObserver(() => {
      if (oldHref !== location.href) {
        oldHref = location.href;
        callback();
      }
    });
    routeObserver.observe(body, { childList: true, subtree: true });

    if (cleanupRouteChange) cleanupRouteChange();

    const _wr = function (type) {
      const orig = history[type];
      return function () {
        const rv = orig.apply(this, arguments);
        window.dispatchEvent(new Event("locationchange"));
        return rv;
      };
    };
    history.pushState = _wr("pushState");
    history.replaceState = _wr("replaceState");

    window.addEventListener("popstate", () => {
      window.dispatchEvent(new Event("locationchange"));
    });
    window.addEventListener("locationchange", callback);

    cleanupRouteChange = function () {
      window.removeEventListener("locationchange", callback);
      if (routeObserver) routeObserver.disconnect();
    };
  }

  function onRouteChange() {
    removeApproveListener();
    removeNextButton(); // 路由切换时销毁"去下一个"按钮
    // 每次路由切换都重新获取todoList接口
    approvalManager.fetchAndStoreList();
    setTimeout(listenApproveButton, 1000);
  }

  // ===================== 启动入口 =====================
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => {
      listenApproveButton();
      listenRouteChange(onRouteChange);
    });
  } else {
    setTimeout(() => {
      listenApproveButton();
      listenRouteChange(onRouteChange);
    }, 1000);
  }
})();
