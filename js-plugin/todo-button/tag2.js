//  这是一个js脚本

(function() {
    // ===================== ApprovalListManager =====================
    class ApprovalListManager {
      constructor() {
        this.listKey = 'approval_list';
        this.indexKey = 'approval_index';
        this.fetchUrl = 'https://pms.tongbaninfo.com:8888/app/api/bpm/workItem/todoList';
        this.fetchOptions = {
          headers: {
            _lang: 'zh_CN',
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            pageNo: 1,
            pageSize: 50,
            param: {
              workTypes: [
                'TRANSFER',
                'BEFORE_APPEND',
                'AFTER_APPEND',
                'SELF',
                'OPINION',
                'AGENT',
                'ORIGINATOR_SUBMIT',
              ],
            },
          }),
          method: 'POST',
          credentials: 'include',
        };
      }
  
      getList() {
        try {
          return JSON.parse(localStorage.getItem(this.listKey)) || [];
        } catch {
          return [];
        }
      }
      setList(list) {
        localStorage.setItem(this.listKey, JSON.stringify(list));
      }
      getIndex() {
        return parseInt(localStorage.getItem(this.indexKey) || '0', 10);
      }
      setIndex(idx) {
        localStorage.setItem(this.indexKey, String(idx));
      }
      async fetchAndStoreList() {
        try {
          const res = await fetch(this.fetchUrl, this.fetchOptions);
          const data = await res.json();
          if (data && data.data && Array.isArray(data.data.body)) {
            const list = data.data.body.map(item => ({
              id: item.data.businessId.value,
              name: item.data.businessId.text || item.data.businessId.value
            }));
            this.setList(list);
          }
        } catch (e) {
          console.error('获取待办列表失败', e);
        }
      }
      async increaseIndexAndMaybeFetch() {
        let idxRaw = localStorage.getItem(this.indexKey);
        let idx = idxRaw === null ? null : parseInt(idxRaw, 10);
        if (idx === null || idx === 8) {
          await this.fetchAndStoreList();
          this.setIndex(1);
        } else {
          this.setIndex(idx + 1);
        }
      }
      removeId(id) {
        const list = this.getList().filter(item => item.id !== id);
        this.setList(list);
      }
      getNextId(currentId) {
        const list = this.getList();
        const next = list.find(item => item.id !== currentId);
        return next ? next.id : null;
      }
    }
  
    // ===================== 按钮监听与路由监听 =====================
    let approveBtn = null;
    let approveBtnHandler = null;
    let routeObserver = null;
    let cleanupRouteChange = null;
    const approvalManager = new ApprovalListManager();
  
    function removeApproveListener() {
      if (approveBtn && approveBtnHandler) {
        approveBtn.removeEventListener('click', approveBtnHandler);
      }
      approveBtn = null;
      approveBtnHandler = null;
    }
  
    function listenApproveButton() {
      removeApproveListener();
      approveBtn = document.querySelector('.buttons .hekit-ui-btn-primary');
      console.log('点击同意按扭=================', approveBtn);
      if (!window.location.href.includes('/approval/view')) return;
      if (!approveBtn) return;
      approveBtnHandler = async function handler() {
        setTimeout(async () => {
          const footer = document.querySelector('.hekit-ui-modal-withfooter-footer');
          if (!footer) return;
          if (footer.querySelector('.custom-next-btn')) return;
          // 插入"同意并去下一个"按钮
          const nextBtn = document.createElement('button');
          nextBtn.type = 'button';
          nextBtn.className = 'hekit-ui-btn hekit-ui-btn-primary custom-next-btn';
          nextBtn.innerHTML = '<span>同意并去下一个</span>';
          nextBtn.onclick = handleNextApprove;
          footer.appendChild(nextBtn);
        }, 300);
        // 计数并每8次拉取一次待办
        await approvalManager.increaseIndexAndMaybeFetch();
      };
      approveBtn.addEventListener('click', approveBtnHandler);
    }
  
    // ===================== "同意并去下一个"按钮逻辑 =====================
    function getCurrentIdFromUrl() {
      const match = window.location.href.match(/id=([^&]+)/);
      return match ? match[1] : null;
    }
  
    function replaceIdInUrl(url, oldId, newId) {
      return url.replace(new RegExp(`id=${oldId}`), `id=${newId}`);
    }
  
    function handleNextApprove() {
      const approvalList = approvalManager.getList();
      const currentId = getCurrentIdFromUrl();
      if (!currentId) {
        console.warn('未能从URL中获取当前id');
        return;
      }
      const nextId = approvalManager.getNextId(currentId);
      if (!nextId) {
        alert('没有更多待办了！');
        return;
      }
      // 跳转到下一个id
      const newUrl = replaceIdInUrl(window.location.href, currentId, nextId);
      approvalManager.removeId(currentId);
      window.location.href = newUrl;
    }
  
    // ===================== 路由监听与内存清理 =====================
    function listenRouteChange(callback) {
      let oldHref = location.href;
      const body = document.querySelector('body');
      if (routeObserver) routeObserver.disconnect();
      routeObserver = new MutationObserver(() => {
        if (oldHref !== location.href) {
          oldHref = location.href;
          callback();
        }
      });
      routeObserver.observe(body, { childList: true, subtree: true });
      if (cleanupRouteChange) cleanupRouteChange();
      const _wr = function(type) {
        const orig = history[type];
        return function() {
          const rv = orig.apply(this, arguments);
          window.dispatchEvent(new Event('locationchange'));
          return rv;
        };
      };
      history.pushState = _wr('pushState');
      history.replaceState = _wr('replaceState');
      window.addEventListener('popstate', () => {
        window.dispatchEvent(new Event('locationchange'));
      });
      window.addEventListener('locationchange', callback);
      cleanupRouteChange = function() {
        window.removeEventListener('locationchange', callback);
        if (routeObserver) routeObserver.disconnect();
      };
    }
  
    function onRouteChange() {
      removeApproveListener();
      setTimeout(listenApproveButton, 3000);
    }
  
    // ===================== 启动入口 =====================
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        listenApproveButton();
        listenRouteChange(onRouteChange);
      });
    } else {
      setTimeout(() => {
        listenApproveButton();
        listenRouteChange(onRouteChange);
      }, 3000);
    }
  })();
  
  