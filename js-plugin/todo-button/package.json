{"name": "todo-button", "version": "1.0.0", "description": "My webpack project", "main": "index.js", "scripts": {"build": "webpack --mode=production --node-env=production", "build:dev": "webpack --mode=development", "start": "cross-env PORT=8704 webpack serve"}, "keywords": [], "author": "", "license": "ISC", "resolutions": {"webpack-cli": "5.0.1"}, "devDependencies": {"webpack-compiler-done": "^1.1.1", "@babel/core": "7.15.8", "@babel/preset-react": "7.14.5", "@types/classnames": "2.2.6", "@types/lodash": "4.14.189", "@types/node": "18.11.15", "@types/react": "17.0.27", "@types/react-dom": "17.0.9", "@webpack-cli/generators": "2.4.0", "autoprefixer": "10.3.7", "babel-loader": "8.2.2", "babel-plugin-import": "1.13.5", "clean-webpack-plugin": "4.0.0", "concurrently": "7.6.0", "cross-env": "7.0.3", "css-loader": "6.3.0", "html-webpack-plugin": "5.3.2", "less": "4.1.2", "less-loader": "10.0.1", "mini-css-extract-plugin": "2.4.2", "postcss-loader": "6.1.1", "style-loader": "3.3.0", "ts-loader": "9.2.6", "typescript": "4.4.3", "webpack": "5.58.0", "webpack-cli": "5.0.1", "webpack-dev-server": "4.11.1", "workbox-webpack-plugin": "6.3.0"}, "dependencies": {"antd": "4.21.2", "react": "17.0.2", "react-dom": "17.0.2"}, "pnpm": {}}