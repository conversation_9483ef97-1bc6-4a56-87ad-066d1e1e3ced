// Generated using webpack-cli https://github.com/webpack/webpack-cli

const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const WebpackCompilerDonePlugin = require('webpack-compiler-done')

const port = process.env.PORT
const isProduction = process.env.NODE_ENV == 'production';

const config = {
	entry: './src/index.tsx',
	output: {
		path: path.resolve(__dirname, 'dist'),
		publicPath: '/',
		filename: 'index.js',
	},
	devServer: {
		port,
		open: true,
		host: 'localhost',
		static: {
			directory: path.join(__dirname, 'public'),
		},
		client: {
			reconnect: false,
		},
		headers: {
			"Access-Control-Allow-Origin": '*'
		}
	},
	plugins: [
		new HtmlWebpackPlugin({
			template: 'index.html',
		}),
	],
	module: {
		rules: [{
				test: /\.(ts|tsx)$/i,
				use: [{
						loader: 'babel-loader',
						options: {
							presets: ['@babel/preset-react']
						}
					},
					'ts-loader',
				],
				exclude: ['/node_modules/'],
			},
			{
				test: /\.less$/i,
				use: ['style-loader', 'css-loader', 'postcss-loader', {
					loader: 'less-loader',
					options: {
						lessOptions: {
							javascriptEnabled: true
						}
					}
				}],
			},
			{
				test: /\.css$/i,
				use: ['style-loader', 'css-loader', 'postcss-loader'],
			},
			{
				test: /\.(eot|svg|ttf|woff|woff2|png|jpg|gif)$/i,
				type: 'asset',
			},
			// Add your rules for custom modules here
			// Learn more about loaders from https://webpack.js.org/loaders/
		],
	},
	resolve: {
		extensions: ['.tsx', '.ts', '.js'],
		alias: {
			'@': path.resolve('src')// 这样配置后 @ 可以指向 src 目录
		}
	},
};

module.exports = () => {
	if (isProduction) {
		config.mode = 'production';
	} else {
		config.mode = 'development';
		config.plugins.push(
			new WebpackCompilerDonePlugin({
				port: 8799,
			})
		)
	}
	return config;
};