### 这是一个脚本说明

fetch("https://pms.tongbaninfo.com:8888/app/api/bpm/workItem/todoList", {
  "headers": {
    "_lang": "zh_CN",
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "application/json",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sw6": "1-MS44Njc2MTkzMTAuMTc0OTgwMDQwOTQxNzAwMDI=-MS44Njc2MTkzMTAuMTc0OTgwMDQwOTQxNzAwMDI=-0-1-1-IzEyNy4wLjAuMTo4MA==-LTE=-LTE="
  },
  "referrer": "https://pms.tongbaninfo.com:8888/app",
  "referrerPolicy": "strict-origin-when-cross-origin",
  "body": "{\"pageNo\":1,\"pageSize\":50,\"param\":{\"workTypes\":[\"TRANSFER\",\"BEFORE_APPEND\",\"AFTER_APPEND\",\"SELF\",\"OPINION\",\"AGENT\",\"ORIGINATOR_SUBMIT\"]}}",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});
如果approveBtn有值的情况下，通过fetch获取上面的接口

返回值是
{
    "code": 200,
    "requestTime": null,
    "appName": null,
    "msg": "服务器成功返回请求的数据",
    "subCode": null,
    "subMsg": null,
    "subMsgType": "success",
    "data": {
        "body": [
            {
                "id": "KQM428398",
                "data": {
                    "modifiedTime": {
                        "text": null,
                        "value": "2025-06-09 18:26:03"
                    },
                    "createUserId": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "deletedDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "headImageUrl": {
                        "text": null,
                        "value": null
                    },
                    "modifyDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "businessId": {
                        "text": "研发1(调试)-工时123162",
                        "value": "KQM3129138"
                    },
                    "businessName": {
                        "text": null,
                        "value": "研发1(调试)-工时123162"
                    },
                    "dataMark": {
                        "text": null,
                        "value": null
                    },
                    "modifyUserId": {
                        "text": null,
                        "value": null
                    },
                    "exceptReason": {
                        "text": null,
                        "value": null
                    },
                    "metadataId": {
                        "text": null,
                        "value": "HW760"
                    },
                    "exceptReasonData": {
                        "text": null,
                        "value": null
                    },
                    "isModified": {
                        "text": null,
                        "value": null
                    },
                    "isTimeOut": {
                        "text": null,
                        "value": null
                    },
                    "createdTime": {
                        "text": null,
                        "value": "2025-06-09 18:26:03"
                    },
                    "completedType": {
                        "text": null,
                        "value": null
                    },
                    "id": {
                        "text": null,
                        "value": "KQM428398"
                    },
                    "overdueTime": {
                        "text": null,
                        "value": null
                    },
                    "processDefId": {
                        "text": null,
                        "value": "KQM984"
                    },
                    "timeOutBackTypeExt": {
                        "text": null,
                        "value": null
                    },
                    "originTime": {
                        "text": null,
                        "value": "2025-06-09 18:26:03"
                    },
                    "processInstName": {
                        "text": null,
                        "value": "研发1(调试)提交的商机交付小组-工时审批"
                    },
                    "processNodeInstName": {
                        "text": null,
                        "value": "直属上级审批"
                    },
                    "deletedUserId": {
                        "text": null,
                        "value": null
                    },
                    "timeOutStatus": {
                        "text": null,
                        "value": null
                    },
                    "companyId": {
                        "text": null,
                        "value": "3701"
                    },
                    "processNodeDefineRDTOList": {
                        "text": null,
                        "value": null
                    },
                    "approvalResult": {
                        "text": null,
                        "value": null
                    },
                    "expectedNextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "processInstId": {
                        "text": null,
                        "value": "KQM121102"
                    },
                    "stepNum": {
                        "text": null,
                        "value": null
                    },
                    "cardKind": {
                        "text": "审批节点",
                        "value": "APPROVAL"
                    },
                    "outTime": {
                        "text": null,
                        "value": null
                    },
                    "originatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "originBusinessName": {
                        "text": null,
                        "value": "研发1(调试)-工时123162"
                    },
                    "processNodeDefName": {
                        "text": null,
                        "value": null
                    },
                    "isEdited": {
                        "text": null,
                        "value": false
                    },
                    "originator": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "processDefineName": {
                        "text": null,
                        "value": "商机交付小组-工时审批"
                    },
                    "participantList": {
                        "text": null,
                        "value": null
                    },
                    "isRevork": {
                        "text": null,
                        "value": null
                    },
                    "isDeleted": {
                        "text": null,
                        "value": "0"
                    },
                    "tenantName": {
                        "text": null,
                        "value": "运营管理系统"
                    },
                    "deletedTagId": {
                        "text": null,
                        "value": null
                    },
                    "userIds": {
                        "text": null,
                        "value": null
                    },
                    "startTime": {
                        "text": null,
                        "value": "2025-06-09 18:26:03"
                    },
                    "deletedTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseName": {
                        "text": null,
                        "value": "上海通办信息服务有限公司"
                    },
                    "nodeDefId": {
                        "text": null,
                        "value": "KQM8887"
                    },
                    "metadataName": {
                        "text": null,
                        "value": "工时记录"
                    },
                    "sourceWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "platformId": {
                        "text": null,
                        "value": null
                    },
                    "originOriginatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "preWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "userId": {
                        "text": null,
                        "value": "KQM1317"
                    },
                    "createDepartmentId": {
                        "text": null,
                        "value": "KQM680"
                    },
                    "incColumns": {
                        "text": null,
                        "value": null
                    },
                    "approverUpdatedBy": {
                        "text": null,
                        "value": null
                    },
                    "nodeInstId": {
                        "text": null,
                        "value": "KQM687874"
                    },
                    "tenantId": {
                        "text": null,
                        "value": "HW44"
                    },
                    "workType": {
                        "text": "自审（默认）",
                        "value": "SELF"
                    },
                    "nextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "describe": {
                        "text": null,
                        "value": null
                    },
                    "endTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseId": {
                        "text": null,
                        "value": "HW71"
                    },
                    "currentState": {
                        "text": "运行中",
                        "value": "RUNNING"
                    },
                    "operateBtnRDTO": {
                        "text": null,
                        "value": null
                    },
                    "processDefVersionId": {
                        "text": null,
                        "value": "KQM1252"
                    }
                },
                "children": null
            },
            {
                "id": "KQM437748",
                "data": {
                    "modifiedTime": {
                        "text": null,
                        "value": "2025-06-06 15:47:36"
                    },
                    "createUserId": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "deletedDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "headImageUrl": {
                        "text": null,
                        "value": null
                    },
                    "modifyDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "businessId": {
                        "text": "研发1(调试)-工时121914",
                        "value": "KQM3121543"
                    },
                    "businessName": {
                        "text": null,
                        "value": "研发1(调试)-工时121914"
                    },
                    "dataMark": {
                        "text": null,
                        "value": null
                    },
                    "modifyUserId": {
                        "text": null,
                        "value": null
                    },
                    "exceptReason": {
                        "text": null,
                        "value": null
                    },
                    "metadataId": {
                        "text": null,
                        "value": "HW760"
                    },
                    "exceptReasonData": {
                        "text": null,
                        "value": null
                    },
                    "isModified": {
                        "text": null,
                        "value": null
                    },
                    "isTimeOut": {
                        "text": null,
                        "value": null
                    },
                    "createdTime": {
                        "text": null,
                        "value": "2025-06-06 15:47:36"
                    },
                    "completedType": {
                        "text": null,
                        "value": null
                    },
                    "id": {
                        "text": null,
                        "value": "KQM437748"
                    },
                    "overdueTime": {
                        "text": null,
                        "value": null
                    },
                    "processDefId": {
                        "text": null,
                        "value": "KQM984"
                    },
                    "timeOutBackTypeExt": {
                        "text": null,
                        "value": null
                    },
                    "originTime": {
                        "text": null,
                        "value": "2025-06-06 15:47:36"
                    },
                    "processInstName": {
                        "text": null,
                        "value": "研发1(调试)提交的商机交付小组-工时审批"
                    },
                    "processNodeInstName": {
                        "text": null,
                        "value": "直属上级审批"
                    },
                    "deletedUserId": {
                        "text": null,
                        "value": null
                    },
                    "timeOutStatus": {
                        "text": null,
                        "value": null
                    },
                    "companyId": {
                        "text": null,
                        "value": "3701"
                    },
                    "processNodeDefineRDTOList": {
                        "text": null,
                        "value": null
                    },
                    "approvalResult": {
                        "text": null,
                        "value": null
                    },
                    "expectedNextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "processInstId": {
                        "text": null,
                        "value": "KQM119730"
                    },
                    "stepNum": {
                        "text": null,
                        "value": null
                    },
                    "cardKind": {
                        "text": "审批节点",
                        "value": "APPROVAL"
                    },
                    "outTime": {
                        "text": null,
                        "value": null
                    },
                    "originatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "originBusinessName": {
                        "text": null,
                        "value": "研发1(调试)-工时121914"
                    },
                    "processNodeDefName": {
                        "text": null,
                        "value": null
                    },
                    "isEdited": {
                        "text": null,
                        "value": false
                    },
                    "originator": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "processDefineName": {
                        "text": null,
                        "value": "商机交付小组-工时审批"
                    },
                    "participantList": {
                        "text": null,
                        "value": null
                    },
                    "isRevork": {
                        "text": null,
                        "value": null
                    },
                    "isDeleted": {
                        "text": null,
                        "value": "0"
                    },
                    "tenantName": {
                        "text": null,
                        "value": "运营管理系统"
                    },
                    "deletedTagId": {
                        "text": null,
                        "value": null
                    },
                    "userIds": {
                        "text": null,
                        "value": null
                    },
                    "startTime": {
                        "text": null,
                        "value": "2025-06-06 15:47:36"
                    },
                    "deletedTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseName": {
                        "text": null,
                        "value": "上海通办信息服务有限公司"
                    },
                    "nodeDefId": {
                        "text": null,
                        "value": "KQM8887"
                    },
                    "metadataName": {
                        "text": null,
                        "value": "工时记录"
                    },
                    "sourceWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "platformId": {
                        "text": null,
                        "value": null
                    },
                    "originOriginatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "preWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "userId": {
                        "text": null,
                        "value": "KQM1317"
                    },
                    "createDepartmentId": {
                        "text": null,
                        "value": "KQM680"
                    },
                    "incColumns": {
                        "text": null,
                        "value": null
                    },
                    "approverUpdatedBy": {
                        "text": null,
                        "value": null
                    },
                    "nodeInstId": {
                        "text": null,
                        "value": "KQM675021"
                    },
                    "tenantId": {
                        "text": null,
                        "value": "HW44"
                    },
                    "workType": {
                        "text": "自审（默认）",
                        "value": "SELF"
                    },
                    "nextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "describe": {
                        "text": null,
                        "value": null
                    },
                    "endTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseId": {
                        "text": null,
                        "value": "HW71"
                    },
                    "currentState": {
                        "text": "运行中",
                        "value": "RUNNING"
                    },
                    "operateBtnRDTO": {
                        "text": null,
                        "value": null
                    },
                    "processDefVersionId": {
                        "text": null,
                        "value": "KQM1252"
                    }
                },
                "children": null
            },
            {
                "id": "KQM420716",
                "data": {
                    "modifiedTime": {
                        "text": null,
                        "value": "2025-06-05 17:54:43"
                    },
                    "createUserId": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "deletedDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "headImageUrl": {
                        "text": null,
                        "value": null
                    },
                    "modifyDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "businessId": {
                        "text": "研发1(调试)-工时121289",
                        "value": "KQM3129657"
                    },
                    "businessName": {
                        "text": null,
                        "value": "研发1(调试)-工时121289"
                    },
                    "dataMark": {
                        "text": null,
                        "value": null
                    },
                    "modifyUserId": {
                        "text": null,
                        "value": null
                    },
                    "exceptReason": {
                        "text": null,
                        "value": null
                    },
                    "metadataId": {
                        "text": null,
                        "value": "HW760"
                    },
                    "exceptReasonData": {
                        "text": null,
                        "value": null
                    },
                    "isModified": {
                        "text": null,
                        "value": null
                    },
                    "isTimeOut": {
                        "text": null,
                        "value": null
                    },
                    "createdTime": {
                        "text": null,
                        "value": "2025-06-05 17:54:43"
                    },
                    "completedType": {
                        "text": null,
                        "value": null
                    },
                    "id": {
                        "text": null,
                        "value": "KQM420716"
                    },
                    "overdueTime": {
                        "text": null,
                        "value": null
                    },
                    "processDefId": {
                        "text": null,
                        "value": "KQM982"
                    },
                    "timeOutBackTypeExt": {
                        "text": null,
                        "value": null
                    },
                    "originTime": {
                        "text": null,
                        "value": "2025-06-05 17:54:43"
                    },
                    "processInstName": {
                        "text": null,
                        "value": "研发1(调试)提交的研发工时-审批流"
                    },
                    "processNodeInstName": {
                        "text": null,
                        "value": "直属上级审批"
                    },
                    "deletedUserId": {
                        "text": null,
                        "value": null
                    },
                    "timeOutStatus": {
                        "text": null,
                        "value": null
                    },
                    "companyId": {
                        "text": null,
                        "value": "3701"
                    },
                    "processNodeDefineRDTOList": {
                        "text": null,
                        "value": null
                    },
                    "approvalResult": {
                        "text": null,
                        "value": null
                    },
                    "expectedNextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "processInstId": {
                        "text": null,
                        "value": "KQM118858"
                    },
                    "stepNum": {
                        "text": null,
                        "value": null
                    },
                    "cardKind": {
                        "text": "审批节点",
                        "value": "APPROVAL"
                    },
                    "outTime": {
                        "text": null,
                        "value": null
                    },
                    "originatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "originBusinessName": {
                        "text": null,
                        "value": "研发1(调试)-工时121289"
                    },
                    "processNodeDefName": {
                        "text": null,
                        "value": null
                    },
                    "isEdited": {
                        "text": null,
                        "value": false
                    },
                    "originator": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "processDefineName": {
                        "text": null,
                        "value": "研发工时-审批流"
                    },
                    "participantList": {
                        "text": null,
                        "value": null
                    },
                    "isRevork": {
                        "text": null,
                        "value": null
                    },
                    "isDeleted": {
                        "text": null,
                        "value": "0"
                    },
                    "tenantName": {
                        "text": null,
                        "value": "运营管理系统"
                    },
                    "deletedTagId": {
                        "text": null,
                        "value": null
                    },
                    "userIds": {
                        "text": null,
                        "value": null
                    },
                    "startTime": {
                        "text": null,
                        "value": "2025-06-05 17:54:43"
                    },
                    "deletedTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseName": {
                        "text": null,
                        "value": "上海通办信息服务有限公司"
                    },
                    "nodeDefId": {
                        "text": null,
                        "value": "KQM8881"
                    },
                    "metadataName": {
                        "text": null,
                        "value": "工时记录"
                    },
                    "sourceWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "platformId": {
                        "text": null,
                        "value": null
                    },
                    "originOriginatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "preWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "userId": {
                        "text": null,
                        "value": "KQM1317"
                    },
                    "createDepartmentId": {
                        "text": null,
                        "value": "KQM680"
                    },
                    "incColumns": {
                        "text": null,
                        "value": null
                    },
                    "approverUpdatedBy": {
                        "text": null,
                        "value": null
                    },
                    "nodeInstId": {
                        "text": null,
                        "value": "KQM671424"
                    },
                    "tenantId": {
                        "text": null,
                        "value": "HW44"
                    },
                    "workType": {
                        "text": "自审（默认）",
                        "value": "SELF"
                    },
                    "nextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "describe": {
                        "text": null,
                        "value": null
                    },
                    "endTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseId": {
                        "text": null,
                        "value": "HW71"
                    },
                    "currentState": {
                        "text": "运行中",
                        "value": "RUNNING"
                    },
                    "operateBtnRDTO": {
                        "text": null,
                        "value": null
                    },
                    "processDefVersionId": {
                        "text": null,
                        "value": "KQM1251"
                    }
                },
                "children": null
            },
            {
                "id": "KQM319553",
                "data": {
                    "modifiedTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "createUserId": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "deletedDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "headImageUrl": {
                        "text": null,
                        "value": null
                    },
                    "modifyDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "businessId": {
                        "text": "研发1(调试)-工时97259",
                        "value": "KQM2964236"
                    },
                    "businessName": {
                        "text": null,
                        "value": "研发1(调试)-工时97259"
                    },
                    "dataMark": {
                        "text": null,
                        "value": null
                    },
                    "modifyUserId": {
                        "text": null,
                        "value": null
                    },
                    "exceptReason": {
                        "text": null,
                        "value": null
                    },
                    "metadataId": {
                        "text": null,
                        "value": "HW760"
                    },
                    "exceptReasonData": {
                        "text": null,
                        "value": null
                    },
                    "isModified": {
                        "text": null,
                        "value": null
                    },
                    "isTimeOut": {
                        "text": null,
                        "value": null
                    },
                    "createdTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "completedType": {
                        "text": null,
                        "value": null
                    },
                    "id": {
                        "text": null,
                        "value": "KQM319553"
                    },
                    "overdueTime": {
                        "text": null,
                        "value": null
                    },
                    "processDefId": {
                        "text": null,
                        "value": "KQM984"
                    },
                    "timeOutBackTypeExt": {
                        "text": null,
                        "value": null
                    },
                    "originTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "processInstName": {
                        "text": null,
                        "value": "研发1(调试)提交的商机交付小组-工时审批"
                    },
                    "processNodeInstName": {
                        "text": null,
                        "value": "直属上级审批"
                    },
                    "deletedUserId": {
                        "text": null,
                        "value": null
                    },
                    "timeOutStatus": {
                        "text": null,
                        "value": null
                    },
                    "companyId": {
                        "text": null,
                        "value": "3701"
                    },
                    "processNodeDefineRDTOList": {
                        "text": null,
                        "value": null
                    },
                    "approvalResult": {
                        "text": null,
                        "value": null
                    },
                    "expectedNextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "processInstId": {
                        "text": null,
                        "value": "KQM96517"
                    },
                    "stepNum": {
                        "text": null,
                        "value": null
                    },
                    "cardKind": {
                        "text": "审批节点",
                        "value": "APPROVAL"
                    },
                    "outTime": {
                        "text": null,
                        "value": null
                    },
                    "originatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "originBusinessName": {
                        "text": null,
                        "value": "研发1(调试)-工时97259"
                    },
                    "processNodeDefName": {
                        "text": null,
                        "value": null
                    },
                    "isEdited": {
                        "text": null,
                        "value": false
                    },
                    "originator": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "processDefineName": {
                        "text": null,
                        "value": "商机交付小组-工时审批"
                    },
                    "participantList": {
                        "text": null,
                        "value": null
                    },
                    "isRevork": {
                        "text": null,
                        "value": null
                    },
                    "isDeleted": {
                        "text": null,
                        "value": "0"
                    },
                    "tenantName": {
                        "text": null,
                        "value": "运营管理系统"
                    },
                    "deletedTagId": {
                        "text": null,
                        "value": null
                    },
                    "userIds": {
                        "text": null,
                        "value": null
                    },
                    "startTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "deletedTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseName": {
                        "text": null,
                        "value": "上海通办信息服务有限公司"
                    },
                    "nodeDefId": {
                        "text": null,
                        "value": "KQM8887"
                    },
                    "metadataName": {
                        "text": null,
                        "value": "工时记录"
                    },
                    "sourceWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "platformId": {
                        "text": null,
                        "value": null
                    },
                    "originOriginatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "preWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "userId": {
                        "text": null,
                        "value": "KQM1317"
                    },
                    "createDepartmentId": {
                        "text": null,
                        "value": "KQM680"
                    },
                    "incColumns": {
                        "text": null,
                        "value": null
                    },
                    "approverUpdatedBy": {
                        "text": null,
                        "value": null
                    },
                    "nodeInstId": {
                        "text": null,
                        "value": "KQM509592"
                    },
                    "tenantId": {
                        "text": null,
                        "value": "HW44"
                    },
                    "workType": {
                        "text": "自审（默认）",
                        "value": "SELF"
                    },
                    "nextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "describe": {
                        "text": null,
                        "value": null
                    },
                    "endTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseId": {
                        "text": null,
                        "value": "HW71"
                    },
                    "currentState": {
                        "text": "运行中",
                        "value": "RUNNING"
                    },
                    "operateBtnRDTO": {
                        "text": null,
                        "value": null
                    },
                    "processDefVersionId": {
                        "text": null,
                        "value": "KQM1252"
                    }
                },
                "children": null
            },
            {
                "id": "KQM319554",
                "data": {
                    "modifiedTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "createUserId": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "deletedDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "headImageUrl": {
                        "text": null,
                        "value": null
                    },
                    "modifyDepartmentId": {
                        "text": null,
                        "value": null
                    },
                    "businessId": {
                        "text": "研发1(调试)-工时97258",
                        "value": "KQM2964235"
                    },
                    "businessName": {
                        "text": null,
                        "value": "研发1(调试)-工时97258"
                    },
                    "dataMark": {
                        "text": null,
                        "value": null
                    },
                    "modifyUserId": {
                        "text": null,
                        "value": null
                    },
                    "exceptReason": {
                        "text": null,
                        "value": null
                    },
                    "metadataId": {
                        "text": null,
                        "value": "HW760"
                    },
                    "exceptReasonData": {
                        "text": null,
                        "value": null
                    },
                    "isModified": {
                        "text": null,
                        "value": null
                    },
                    "isTimeOut": {
                        "text": null,
                        "value": null
                    },
                    "createdTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "completedType": {
                        "text": null,
                        "value": null
                    },
                    "id": {
                        "text": null,
                        "value": "KQM319554"
                    },
                    "overdueTime": {
                        "text": null,
                        "value": null
                    },
                    "processDefId": {
                        "text": null,
                        "value": "KQM984"
                    },
                    "timeOutBackTypeExt": {
                        "text": null,
                        "value": null
                    },
                    "originTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "processInstName": {
                        "text": null,
                        "value": "研发1(调试)提交的商机交付小组-工时审批"
                    },
                    "processNodeInstName": {
                        "text": null,
                        "value": "直属上级审批"
                    },
                    "deletedUserId": {
                        "text": null,
                        "value": null
                    },
                    "timeOutStatus": {
                        "text": null,
                        "value": null
                    },
                    "companyId": {
                        "text": null,
                        "value": "3701"
                    },
                    "processNodeDefineRDTOList": {
                        "text": null,
                        "value": null
                    },
                    "approvalResult": {
                        "text": null,
                        "value": null
                    },
                    "expectedNextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "processInstId": {
                        "text": null,
                        "value": "KQM96516"
                    },
                    "stepNum": {
                        "text": null,
                        "value": null
                    },
                    "cardKind": {
                        "text": "审批节点",
                        "value": "APPROVAL"
                    },
                    "outTime": {
                        "text": null,
                        "value": null
                    },
                    "originatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "originBusinessName": {
                        "text": null,
                        "value": "研发1(调试)-工时97258"
                    },
                    "processNodeDefName": {
                        "text": null,
                        "value": null
                    },
                    "isEdited": {
                        "text": null,
                        "value": false
                    },
                    "originator": {
                        "text": "研发1(调试)",
                        "value": "KQM1316",
                        "positionStatus": "onTheJob"
                    },
                    "processDefineName": {
                        "text": null,
                        "value": "商机交付小组-工时审批"
                    },
                    "participantList": {
                        "text": null,
                        "value": null
                    },
                    "isRevork": {
                        "text": null,
                        "value": null
                    },
                    "isDeleted": {
                        "text": null,
                        "value": "0"
                    },
                    "tenantName": {
                        "text": null,
                        "value": "运营管理系统"
                    },
                    "deletedTagId": {
                        "text": null,
                        "value": null
                    },
                    "userIds": {
                        "text": null,
                        "value": null
                    },
                    "startTime": {
                        "text": null,
                        "value": "2025-04-16 16:30:34"
                    },
                    "deletedTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseName": {
                        "text": null,
                        "value": "上海通办信息服务有限公司"
                    },
                    "nodeDefId": {
                        "text": null,
                        "value": "KQM8887"
                    },
                    "metadataName": {
                        "text": null,
                        "value": "工时记录"
                    },
                    "sourceWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "platformId": {
                        "text": null,
                        "value": null
                    },
                    "originOriginatorName": {
                        "text": null,
                        "value": "研发1(调试)"
                    },
                    "preWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "userId": {
                        "text": null,
                        "value": "KQM1317"
                    },
                    "createDepartmentId": {
                        "text": null,
                        "value": "KQM680"
                    },
                    "incColumns": {
                        "text": null,
                        "value": null
                    },
                    "approverUpdatedBy": {
                        "text": null,
                        "value": null
                    },
                    "nodeInstId": {
                        "text": null,
                        "value": "KQM509591"
                    },
                    "tenantId": {
                        "text": null,
                        "value": "HW44"
                    },
                    "workType": {
                        "text": "自审（默认）",
                        "value": "SELF"
                    },
                    "nextWorkItemId": {
                        "text": null,
                        "value": null
                    },
                    "describe": {
                        "text": null,
                        "value": null
                    },
                    "endTime": {
                        "text": null,
                        "value": null
                    },
                    "enterpriseId": {
                        "text": null,
                        "value": "HW71"
                    },
                    "currentState": {
                        "text": "运行中",
                        "value": "RUNNING"
                    },
                    "operateBtnRDTO": {
                        "text": null,
                        "value": null
                    },
                    "processDefVersionId": {
                        "text": null,
                        "value": "KQM1252"
                    }
                },
                "children": null
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 50,
            "total": 5,
            "pages": null,
            "hasMorePage": false,
            "dimTotal": null
        },
        "viewId": null,
        "viewName": null
    },
    "tid": null,
    "originalTid": null
}
我需要存取一个list 存[{id:xxx,name:"xx'}]id存返回值里面businessId的value name存返回值里面businessId的text
然后把他存到localstorage里面，key为"approval_list"
再存取一个值为approval_index 每approveBtn每8次有值就重新获取下上面的接口 并存取到localstorage里面
当点击【同意去下一个】按扭的时候就从localstorage里面获取approval_list 的数据，然后获取window.location.href的值 如下【https://pms.tongbaninfo.com:8888/app#/approval/view?pageType=view&id=KQM3129657&processInstId=KQM121102&processIntsId=KQM121102&standardCollectionId=HW760&nodeDefId=KQM8887&processDefVersionId=KQM1252&companyId=3701&tenantId=HW44&mainStandardBOId=HW760】匹配id拿到一个非本[id=KQM3129657]的id 
然后替换[id=KQM3129657]为[id=xxx] 然后跳转过去 并从localstorage里面删除approval_list里面当前的id







```