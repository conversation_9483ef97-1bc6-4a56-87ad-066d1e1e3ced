/**
 * 级联选择组件相关类型定义
 */

// 运营服务类型
export interface OperationalService {
  id: string;
  type: string; // 运营服务类型
  description: string; // 运营服务说明
  unit: string; // 计量单位
  quantity: number; // 数量
  editable: boolean; // 是否可编辑数量
}

// 必选产品
export interface RequiredProduct {
  id: string;
  name: string;
  description: string;
  selected: boolean; // 是否被选中
  disabled: boolean; // 是否禁用（必选产品在场景选中时不可取消）
}

// 服务场景
export interface ServiceScenario {
  id: string;
  name: string; // 场景名称
  count: number; // 产品数量
  selected: boolean; // 是否被选中
  description: string; // 场景简介
  requiredProducts: RequiredProduct[]; // 必选产品列表
  operationalServices: OperationalService[]; // 运营服务明细
}

// 商机项目建设清单项
export interface ProjectBuildItem {
  id: string;
  productName: string; // 产品项目名称
  serviceContent: string; // 运营服务内容
  measurementMethod: string; // 计量方式
  quantity: number; // 数量
  buildStatus: string; // 商机项目建设状态
  scenarioId: string; // 关联的服务场景ID
  serviceId: string; // 关联的运营服务ID
}

// 级联选择组件的状态
export interface CascadeState {
  serviceScenarios: ServiceScenario[]; // 服务场景列表
  selectedScenarioId: string | null; // 当前选中的场景ID
  projectBuildItems: ProjectBuildItem[]; // 商机项目建设清单
}

// 级联选择组件的Props
export interface CascadeProps {
  initialData?: ServiceScenario[]; // 初始数据
  onSelectionChange?: (selectedItems: ProjectBuildItem[]) => void; // 选择变化回调
  onQuantityChange?: (itemId: string, quantity: number) => void; // 数量变化回调
}

// 服务场景列表组件Props
export interface ServiceScenarioListProps {
  scenarios: ServiceScenario[];
  selectedScenarioId: string | null;
  onScenarioSelect: (scenarioId: string) => void;
  onScenarioToggle: (scenarioId: string, selected: boolean) => void;
}

// 产品详情组件Props
export interface ProductDetailsProps {
  scenario: ServiceScenario | null;
  onQuantityChange: (serviceId: string, quantity: number) => void;
}

// 商机项目建设清单组件Props
export interface ProjectBuildListProps {
  items: ProjectBuildItem[];
  onQuantityChange: (itemId: string, quantity: number) => void;
}

// 运营服务明细表格Props
export interface OperationalServiceTableProps {
  services: OperationalService[];
  onQuantityChange: (serviceId: string, quantity: number) => void;
}
