// 主要组件导出
export { default as Cascade } from './components/Cascade';
export { default as ServiceScenarioList } from './components/ServiceScenarioList';
export { default as ProductDetails } from './components/ProductDetails';
export { default as OperationalServiceTable } from './components/OperationalServiceTable';
export { default as ProjectBuildList } from './components/ProjectBuildList';

// 类型定义导出
export type {
  ServiceScenario,
  RequiredProduct,
  OperationalService,
  ProjectBuildItem,
  CascadeState,
  CascadeProps,
  ServiceScenarioListProps,
  ProductDetailsProps,
  OperationalServiceTableProps,
  ProjectBuildListProps
} from './types/cascade';

// 工具函数导出
export {
  generateProjectBuildItems,
  updateScenarioSelection,
  generateCompleteProjectBuildList,
  updateProjectBuildItemQuantity,
  updateServiceQuantityAndSync,
  removeProjectBuildItemsByScenario
} from './utils/cascadeUtils';

// 模拟数据导出
export { mockServiceScenarios } from './data/mockData';

// 演示页面导出
export { default as CascadeDemo } from './pages/CascadeDemo';
