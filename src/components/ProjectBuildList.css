.project-build-list {
  margin-top: 24px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.list-summary {
  font-size: 14px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 12px;
  border-radius: 12px;
}

.list-content {
  padding: 0;
}

.build-list-table .ant-table-thead > tr > th {
  background: #f8f9fa;
  font-weight: 600;
  font-size: 13px;
  color: #333;
  border-bottom: 2px solid #e9ecef;
}

.build-list-table .ant-table-tbody > tr > td {
  padding: 12px 16px;
  font-size: 13px;
  vertical-align: top;
}

.build-list-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.product-name-cell {
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.service-content-cell {
  color: #666;
}

.measurement-method-cell {
  color: #666;
}

.quantity-edit-cell {
  display: flex;
  align-items: center;
}

.quantity-edit-cell .ant-input-number {
  border-radius: 4px;
}

.quantity-edit-cell .ant-input-number-input {
  text-align: center;
  font-size: 13px;
}

/* 分页样式调整 */
.build-list-table .ant-pagination {
  margin: 16px 20px;
  text-align: right;
}

.build-list-table .ant-pagination .ant-pagination-total-text {
  font-size: 12px;
  color: #666;
}

/* 空状态样式 */
.build-list-table .ant-empty {
  padding: 40px 20px;
}

.build-list-table .ant-empty-description {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .build-list-table .ant-table-thead > tr > th,
  .build-list-table .ant-table-tbody > tr > td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .list-header {
    padding: 12px 16px;
  }
  
  .list-title {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .build-list-table .ant-table {
    font-size: 11px;
  }
  
  .quantity-edit-cell .ant-input-number {
    width: 60px;
  }
}
