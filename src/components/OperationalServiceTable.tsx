import React from 'react';
import { Table, InputNumber } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { OperationalServiceTableProps, OperationalService } from '../types/cascade';
import './OperationalServiceTable.css';

/**
 * 运营服务明细表格组件
 * 显示运营服务详情，支持数量编辑
 */
const OperationalServiceTable: React.FC<OperationalServiceTableProps> = ({
  services,
  onQuantityChange
}) => {
  const handleQuantityChange = (serviceId: string, value: number | null) => {
    if (value !== null && value >= 0) {
      onQuantityChange(serviceId, value);
    }
  };

  const columns: ColumnsType<OperationalService> = [
    {
      title: '运营服务类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (text: string) => (
        <span className="service-type">{text}</span>
      )
    },
    {
      title: '运营服务说明',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => (
        <span className="service-description">{text}</span>
      )
    },
    {
      title: '数量',
      key: 'quantity',
      width: 120,
      render: (_, record: OperationalService) => (
        <div className="quantity-cell">
          {record.editable ? (
            <InputNumber
              min={0}
              value={record.quantity}
              onChange={(value) => handleQuantityChange(record.id, value)}
              size="small"
              style={{ width: 60 }}
            />
          ) : (
            <span>{record.quantity}</span>
          )}
          <span className="unit">{record.unit}</span>
        </div>
      )
    }
  ];

  // 计算总数
  const totalCount = services.reduce((sum, service) => sum + service.quantity, 0);

  return (
    <div className="operational-service-table">
      <div className="table-header">
        <span className="table-title">运营服务清单</span>
        <span className="total-count">总计: {totalCount}</span>
      </div>
      <Table
        columns={columns}
        dataSource={services}
        rowKey="id"
        pagination={false}
        size="small"
        className="service-table"
        scroll={{ y: 300 }}
      />
    </div>
  );
};

export default OperationalServiceTable;
