import React from 'react';
import { Card, Checkbox, Empty } from 'antd';
import OperationalServiceTable from './OperationalServiceTable';
import { ProductDetailsProps } from '../types/cascade';
import './ProductDetails.css';

/**
 * 产品详情组件
 * 显示右侧的产品详情，包含服务场景简介、必选产品列表、运营服务明细
 */
const ProductDetails: React.FC<ProductDetailsProps> = ({
  scenario,
  onQuantityChange
}) => {
  if (!scenario) {
    return (
      <div className="product-details">
        <div className="details-header">
          选择对应产品
        </div>
        <div className="empty-content">
          <Empty 
            description="请选择左侧服务场景查看详情"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="product-details">
      <div className="details-header">
        选择对应产品
      </div>
      
      <div className="details-content">
        {/* 服务场景简介 */}
        <Card 
          title="服务场景简介" 
          size="small" 
          className="scenario-intro-card"
        >
          <p className="scenario-description">
            {scenario.description}
          </p>
        </Card>

        {/* 产品目录清单 */}
        <Card 
          title="产品目录清单" 
          size="small" 
          className="product-catalog-card"
        >
          <div className="required-products-section">
            <div className="section-title">
              必选产品 ({scenario.requiredProducts.length})
            </div>
            <div className="products-list">
              {scenario.requiredProducts.map((product) => (
                <div key={product.id} className="product-item">
                  <div className="product-checkbox">
                    <Checkbox
                      checked={product.selected}
                      disabled={product.disabled}
                    />
                  </div>
                  <div className="product-info">
                    <div className="product-name">{product.name}</div>
                    <div className="product-description">
                      {product.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 运营服务明细 */}
          {scenario.operationalServices.length > 0 && (
            <OperationalServiceTable
              services={scenario.operationalServices}
              onQuantityChange={onQuantityChange}
            />
          )}

          {/* 运营服务清单为空时的提示 */}
          {scenario.operationalServices.length === 0 && (
            <div className="empty-services">
              <Empty 
                description="暂无运营服务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                style={{ margin: '20px 0' }}
              />
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ProductDetails;
