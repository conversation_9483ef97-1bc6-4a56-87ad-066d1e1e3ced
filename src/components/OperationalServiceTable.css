.operational-service-table {
  margin-top: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.table-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.total-count {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.service-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 500;
  font-size: 12px;
  padding: 8px 12px;
}

.service-table .ant-table-tbody > tr > td {
  padding: 8px 12px;
  font-size: 12px;
}

.service-type {
  color: #333;
  font-weight: 500;
}

.service-description {
  color: #666;
}

.quantity-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  color: #666;
  font-size: 11px;
}

/* 表格行悬停效果 */
.service-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 输入框样式调整 */
.quantity-cell .ant-input-number {
  border-radius: 4px;
}

.quantity-cell .ant-input-number-input {
  text-align: center;
  font-size: 12px;
}
