import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col } from 'antd';
import ServiceScenarioList from './ServiceScenarioList';
import ProductDetails from './ProductDetails';
import ProjectBuildList from './ProjectBuildList';
import { CascadeProps, ServiceScenario, ProjectBuildItem } from '../types/cascade';
import {
  updateScenarioSelection,
  generateCompleteProjectBuildList,
  updateProjectBuildItemQuantity,
  updateServiceQuantityAndSync,
  removeProjectBuildItemsByScenario
} from '../utils/cascadeUtils';
import './Cascade.css';

/**
 * 级联选择主组件
 * 整合所有子组件，实现完整的级联选择功能
 */
const Cascade: React.FC<CascadeProps> = ({
  initialData = [],
  onSelectionChange,
  onQuantityChange
}) => {
  // 状态管理
  const [scenarios, setScenarios] = useState<ServiceScenario[]>(initialData);
  const [selectedScenarioId, setSelectedScenarioId] = useState<string | null>(null);
  const [projectBuildItems, setProjectBuildItems] = useState<ProjectBuildItem[]>([]);

  // 初始化时选择第一个场景
  useEffect(() => {
    if (scenarios.length > 0 && !selectedScenarioId) {
      setSelectedScenarioId(scenarios[0].id);
    }
  }, [scenarios, selectedScenarioId]);

  // 当场景选择状态变化时，更新商机项目建设清单
  useEffect(() => {
    const newProjectItems = generateCompleteProjectBuildList(scenarios);
    setProjectBuildItems(newProjectItems);
    
    // 通知父组件选择变化
    if (onSelectionChange) {
      onSelectionChange(newProjectItems);
    }
  }, [scenarios, onSelectionChange]);

  // 处理服务场景选择
  const handleScenarioSelect = useCallback((scenarioId: string) => {
    setSelectedScenarioId(scenarioId);
  }, []);

  // 处理服务场景勾选/取消勾选
  const handleScenarioToggle = useCallback((scenarioId: string, selected: boolean) => {
    setScenarios(prevScenarios => {
      const updatedScenarios = updateScenarioSelection(prevScenarios, scenarioId, selected);
      
      // 如果取消勾选，移除相关的商机项目建设清单项
      if (!selected) {
        setProjectBuildItems(prevItems => 
          removeProjectBuildItemsByScenario(prevItems, scenarioId)
        );
      }
      
      return updatedScenarios;
    });
  }, []);

  // 处理运营服务数量变化
  const handleServiceQuantityChange = useCallback((serviceId: string, quantity: number) => {
    if (!selectedScenarioId) return;

    const result = updateServiceQuantityAndSync(
      scenarios,
      projectBuildItems,
      selectedScenarioId,
      serviceId,
      quantity
    );

    setScenarios(result.scenarios);
    setProjectBuildItems(result.projectItems);

    // 通知父组件数量变化
    if (onQuantityChange) {
      onQuantityChange(serviceId, quantity);
    }
  }, [scenarios, projectBuildItems, selectedScenarioId, onQuantityChange]);

  // 处理商机项目建设清单数量变化
  const handleProjectItemQuantityChange = useCallback((itemId: string, quantity: number) => {
    setProjectBuildItems(prevItems => 
      updateProjectBuildItemQuantity(prevItems, itemId, quantity)
    );

    // 同步更新到服务场景中
    const item = projectBuildItems.find(item => item.id === itemId);
    if (item && item.serviceId !== 'default') {
      handleServiceQuantityChange(item.serviceId, quantity);
    }
  }, [projectBuildItems, handleServiceQuantityChange]);

  // 获取当前选中的场景
  const selectedScenario = scenarios.find(scenario => scenario.id === selectedScenarioId) || null;

  return (
    <div className="cascade-container">
      <Row gutter={16} className="cascade-main">
        {/* 左侧服务场景列表 */}
        <Col xs={24} md={8} lg={6} className="scenario-list-col">
          <ServiceScenarioList
            scenarios={scenarios}
            selectedScenarioId={selectedScenarioId}
            onScenarioSelect={handleScenarioSelect}
            onScenarioToggle={handleScenarioToggle}
          />
        </Col>

        {/* 右侧产品详情 */}
        <Col xs={24} md={16} lg={18} className="product-details-col">
          <ProductDetails
            scenario={selectedScenario}
            onQuantityChange={handleServiceQuantityChange}
          />
        </Col>
      </Row>

      {/* 商机项目建设清单 */}
      <div className="project-build-section">
        <ProjectBuildList
          items={projectBuildItems}
          onQuantityChange={handleProjectItemQuantityChange}
        />
      </div>
    </div>
  );
};

export default Cascade;
