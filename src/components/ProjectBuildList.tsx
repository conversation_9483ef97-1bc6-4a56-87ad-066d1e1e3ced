import React from 'react';
import { Table, InputNumber, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { ProjectBuildListProps, ProjectBuildItem } from '../types/cascade';
import './ProjectBuildList.css';

/**
 * 商机项目建设清单组件
 * 显示已选择的产品和服务，支持数量编辑
 */
const ProjectBuildList: React.FC<ProjectBuildListProps> = ({
  items,
  onQuantityChange
}) => {
  const handleQuantityChange = (itemId: string, value: number | null) => {
    if (value !== null && value >= 0) {
      onQuantityChange(itemId, value);
    }
  };

  const columns: ColumnsType<ProjectBuildItem> = [
    {
      title: '产品项目名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 300,
      render: (text: string) => (
        <span className="product-name-cell">{text}</span>
      )
    },
    {
      title: '运营服务内容',
      dataIndex: 'serviceContent',
      key: 'serviceContent',
      width: 150,
      render: (text: string) => (
        <span className="service-content-cell">{text || '-'}</span>
      )
    },
    {
      title: '计量方式',
      dataIndex: 'measurementMethod',
      key: 'measurementMethod',
      width: 120,
      render: (text: string) => (
        <span className="measurement-method-cell">{text || '-'}</span>
      )
    },
    {
      title: '数量',
      key: 'quantity',
      width: 100,
      render: (_, record: ProjectBuildItem) => (
        <div className="quantity-edit-cell">
          <InputNumber
            min={0}
            value={record.quantity}
            onChange={(value) => handleQuantityChange(record.id, value)}
            size="small"
            style={{ width: 70 }}
          />
        </div>
      )
    },
    {
      title: '商机项目建设状态',
      dataIndex: 'buildStatus',
      key: 'buildStatus',
      width: 120,
      render: (status: string) => (
        <Tag color={status === '未建设' ? 'default' : 'green'}>
          {status}
        </Tag>
      )
    }
  ];

  return (
    <div className="project-build-list">
      <div className="list-header">
        <h3 className="list-title">商机项目建设清单</h3>
        <div className="list-summary">
          共 {items.length} 项
        </div>
      </div>
      
      <div className="list-content">
        <Table
          columns={columns}
          dataSource={items}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50']
          }}
          size="small"
          className="build-list-table"
          locale={{
            emptyText: '暂无数据，请选择左侧服务场景'
          }}
        />
      </div>
    </div>
  );
};

export default ProjectBuildList;
