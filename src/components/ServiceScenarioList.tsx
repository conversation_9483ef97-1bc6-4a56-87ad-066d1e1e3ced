import React from 'react';
import { Checkbox } from 'antd';
import { ServiceScenarioListProps } from '../types/cascade';
import './ServiceScenarioList.css';

/**
 * 服务场景列表组件
 * 显示左侧的服务场景列表，支持勾选和点击切换详情
 */
const ServiceScenarioList: React.FC<ServiceScenarioListProps> = ({
  scenarios,
  selectedScenarioId,
  onScenarioSelect,
  onScenarioToggle
}) => {
  const handleCheckboxChange = (scenarioId: string, checked: boolean) => {
    onScenarioToggle(scenarioId, checked);
  };

  const handleItemClick = (scenarioId: string) => {
    onScenarioSelect(scenarioId);
  };

  return (
    <div className="service-scenario-list">
      <div className="scenario-list-header">
        选择服务场景
      </div>
      <div className="scenario-list-content">
        {scenarios.map((scenario) => (
          <div
            key={scenario.id}
            className={`scenario-item ${selectedScenarioId === scenario.id ? 'active' : ''}`}
          >
            <div className="scenario-checkbox">
              <Checkbox
                checked={scenario.selected}
                onChange={(e) => handleCheckboxChange(scenario.id, e.target.checked)}
              />
            </div>
            <div
              className="scenario-info"
              onClick={() => handleItemClick(scenario.id)}
            >
              <span className="scenario-name">{scenario.name}</span>
              <span className="scenario-count">({scenario.count})</span>
            </div>
            <div className="scenario-arrow">
              <span>›</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ServiceScenarioList;
