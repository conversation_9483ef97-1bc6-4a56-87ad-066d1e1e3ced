.product-details {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.details-header {
  background: #4a90e2;
  color: white;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.details-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.scenario-intro-card {
  margin-bottom: 16px;
}

.scenario-intro-card .ant-card-head {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.scenario-intro-card .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

.scenario-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 13px;
}

.product-catalog-card .ant-card-head {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.product-catalog-card .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

.required-products-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f0f0f0;
  border-radius: 4px;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.product-checkbox {
  margin-right: 12px;
  margin-top: 2px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.product-description {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.empty-services {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .details-content {
    padding: 12px;
  }
  
  .product-item {
    padding: 10px;
  }
  
  .product-name {
    font-size: 13px;
  }
  
  .product-description {
    font-size: 11px;
  }
}
