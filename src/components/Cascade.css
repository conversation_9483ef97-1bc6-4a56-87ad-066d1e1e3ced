.cascade-container {
  width: 100%;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
}

.cascade-main {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 16px;
}

.scenario-list-col {
  padding: 0 !important;
  border-right: 1px solid #e8e8e8;
}

.product-details-col {
  padding: 0 !important;
}

.project-build-section {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cascade-container {
    padding: 8px;
  }
  
  .cascade-main {
    margin-bottom: 12px;
  }
  
  .scenario-list-col {
    border-right: none;
    border-bottom: 1px solid #e8e8e8;
  }
  
  .cascade-main .ant-row {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .cascade-container {
    padding: 4px;
  }
}

/* 确保组件高度适配 */
.cascade-main .ant-col {
  min-height: 600px;
}

@media (max-width: 768px) {
  .cascade-main .ant-col {
    min-height: auto;
  }
  
  .scenario-list-col {
    min-height: 300px;
  }
  
  .product-details-col {
    min-height: 400px;
  }
}
