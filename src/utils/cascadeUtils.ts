import { ServiceScenario, ProjectBuildItem, OperationalService } from '../types/cascade';

/**
 * 级联选择相关工具函数
 */

/**
 * 根据服务场景生成商机项目建设清单项
 */
export const generateProjectBuildItems = (scenario: ServiceScenario): ProjectBuildItem[] => {
  const items: ProjectBuildItem[] = [];

  // 为每个运营服务生成一个建设清单项
  scenario.operationalServices.forEach((service: OperationalService) => {
    // 根据必选产品生成产品名称
    const productName = scenario.requiredProducts.length > 0 
      ? `2024年"${scenario.name}"${scenario.requiredProducts[0].name}V2.0产品项目`
      : `2024年${scenario.name}V1.0产品项目`;

    items.push({
      id: `${scenario.id}-${service.id}`,
      productName,
      serviceContent: service.type,
      measurementMethod: `${service.unit}数量 (${service.unit})`,
      quantity: service.quantity,
      buildStatus: '未建设',
      scenarioId: scenario.id,
      serviceId: service.id
    });
  });

  // 如果没有运营服务但有必选产品，也生成一个项目
  if (scenario.operationalServices.length === 0 && scenario.requiredProducts.length > 0) {
    items.push({
      id: `${scenario.id}-default`,
      productName: `2024年${scenario.name}V2.0产品项目`,
      serviceContent: '-',
      measurementMethod: '-',
      quantity: 0,
      buildStatus: '未建设',
      scenarioId: scenario.id,
      serviceId: 'default'
    });
  }

  return items;
};

/**
 * 更新服务场景的选中状态和必选产品状态
 */
export const updateScenarioSelection = (
  scenarios: ServiceScenario[],
  scenarioId: string,
  selected: boolean
): ServiceScenario[] => {
  return scenarios.map(scenario => {
    if (scenario.id === scenarioId) {
      return {
        ...scenario,
        selected,
        // 当场景被选中时，必选产品自动选中且禁用；取消选中时恢复
        requiredProducts: scenario.requiredProducts.map(product => ({
          ...product,
          selected: selected,
          disabled: selected
        }))
      };
    }
    return scenario;
  });
};

/**
 * 根据选中的服务场景生成完整的商机项目建设清单
 */
export const generateCompleteProjectBuildList = (scenarios: ServiceScenario[]): ProjectBuildItem[] => {
  const selectedScenarios = scenarios.filter(scenario => scenario.selected);
  const allItems: ProjectBuildItem[] = [];

  selectedScenarios.forEach(scenario => {
    const items = generateProjectBuildItems(scenario);
    allItems.push(...items);
  });

  return allItems;
};

/**
 * 更新商机项目建设清单中的数量
 */
export const updateProjectBuildItemQuantity = (
  items: ProjectBuildItem[],
  itemId: string,
  quantity: number
): ProjectBuildItem[] => {
  return items.map(item => {
    if (item.id === itemId) {
      return {
        ...item,
        quantity
      };
    }
    return item;
  });
};

/**
 * 更新服务场景中运营服务的数量，并同步到商机项目建设清单
 */
export const updateServiceQuantityAndSync = (
  scenarios: ServiceScenario[],
  projectItems: ProjectBuildItem[],
  scenarioId: string,
  serviceId: string,
  quantity: number
): { scenarios: ServiceScenario[]; projectItems: ProjectBuildItem[] } => {
  // 更新服务场景中的数量
  const updatedScenarios = scenarios.map(scenario => {
    if (scenario.id === scenarioId) {
      return {
        ...scenario,
        operationalServices: scenario.operationalServices.map(service => {
          if (service.id === serviceId) {
            return {
              ...service,
              quantity
            };
          }
          return service;
        })
      };
    }
    return scenario;
  });

  // 同步更新商机项目建设清单
  const updatedProjectItems = projectItems.map(item => {
    if (item.scenarioId === scenarioId && item.serviceId === serviceId) {
      return {
        ...item,
        quantity
      };
    }
    return item;
  });

  return {
    scenarios: updatedScenarios,
    projectItems: updatedProjectItems
  };
};

/**
 * 移除指定服务场景相关的商机项目建设清单项
 */
export const removeProjectBuildItemsByScenario = (
  items: ProjectBuildItem[],
  scenarioId: string
): ProjectBuildItem[] => {
  return items.filter(item => item.scenarioId !== scenarioId);
};
