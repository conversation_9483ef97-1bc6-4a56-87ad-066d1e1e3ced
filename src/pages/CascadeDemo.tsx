import React from 'react';
import { message } from 'antd';
import Cascade from '../components/Cascade';
import { mockServiceScenarios } from '../data/mockData';
import { ProjectBuildItem } from '../types/cascade';

/**
 * 级联选择演示页面
 */
const CascadeDemo: React.FC = () => {
  // 处理选择变化
  const handleSelectionChange = (selectedItems: ProjectBuildItem[]) => {
    console.log('选择变化:', selectedItems);
    message.info(`已选择 ${selectedItems.length} 个项目`);
  };

  // 处理数量变化
  const handleQuantityChange = (itemId: string, quantity: number) => {
    console.log('数量变化:', { itemId, quantity });
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ textAlign: 'center', marginBottom: '20px' }}>
        级联选择组件演示
      </h1>
      <Cascade
        initialData={mockServiceScenarios}
        onSelectionChange={handleSelectionChange}
        onQuantityChange={handleQuantityChange}
      />
    </div>
  );
};

export default CascadeDemo;
