import { ServiceScenario } from '../types/cascade';

/**
 * 模拟数据 - 基于UI截图内容
 */
export const mockServiceScenarios: ServiceScenario[] = [
  {
    id: 'scenario-1',
    name: '好办一件事',
    count: 8,
    selected: false,
    description: '"好办"或"一件事"是近年来政府政务服务领域推出的便民服务产品，旨在通过整合多部门、多事项，实现"一次申请、一套材料、一网通办、一窗受理"的一体化办理模式，为企业和群众提供更高效、便捷的政务服务。',
    requiredProducts: [
      {
        id: 'product-1-1',
        name: '智能构建系统/智能业务运营系统',
        description: '"高效办成一件事"智能构建系统，通过流程梳理、智能推荐与组件复用，实现政务服务事项快速配置、智能生成和一体化办理，全面提升办事效率上线效率与群众办事体验。',
        selected: false,
        disabled: false
      }
    ],
    operationalServices: [
      {
        id: 'service-1-1',
        type: '集成办服务',
        description: 'XXXXXXXX',
        unit: '主题',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-2',
        type: '跨域办服务',
        description: 'XXXXXXXX',
        unit: '事项',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-3',
        type: '免申办服务',
        description: 'XXXXXXXX',
        unit: '政策',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-4',
        type: '承诺办服务',
        description: 'XXXXXXXX',
        unit: '事项',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-5',
        type: '一类事一站办服务',
        description: 'XXXXXXXX',
        unit: '主题',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-6',
        type: '一业一证服务',
        description: 'XXXXXXXX',
        unit: '主题',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-7',
        type: '智慧好办服务',
        description: 'XXXXXXXX',
        unit: '事项',
        quantity: 3,
        editable: true
      },
      {
        id: 'service-1-8',
        type: '政策申报服务',
        description: 'XXXXXXXX',
        unit: '申报项目',
        quantity: 3,
        editable: true
      }
    ]
  },
  {
    id: 'scenario-2',
    name: '智能服务中枢',
    count: 2,
    selected: false,
    description: '智能服务中枢是政务服务的核心平台，提供统一的服务入口和智能化的服务推荐。',
    requiredProducts: [
      {
        id: 'product-2-1',
        name: '用户行为调度系统',
        description: '用户行为库通过采集用户在政务服务平台上的行为数据，构建用户行为画像，支持精细化服务推荐、智能分析与产品优化决策，推动政务服务从"可用"向"好用、易用、智能化"演进。',
        selected: false,
        disabled: false
      }
    ],
    operationalServices: []
  },
  {
    id: 'scenario-3',
    name: '事项梳理',
    count: 1,
    selected: false,
    description: '事项梳理服务帮助政府部门规范和优化政务服务事项。',
    requiredProducts: [],
    operationalServices: []
  },
  {
    id: 'scenario-4',
    name: '办事指南校验',
    count: 1,
    selected: false,
    description: '办事指南校验服务确保政务服务指南的准确性和完整性。',
    requiredProducts: [],
    operationalServices: []
  },
  {
    id: 'scenario-5',
    name: '材料预审',
    count: 4,
    selected: false,
    description: '材料预审服务提供智能化的材料审核和预处理功能。',
    requiredProducts: [],
    operationalServices: []
  }
];
