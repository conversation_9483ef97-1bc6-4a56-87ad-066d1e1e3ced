/**
 * 合同管理相关类型定义
 */

// 合同类型枚举
export enum ContractType {
  DEVELOPMENT = '开发合同',
  SERVICE = '服务合同',
  OTHER = '其他'
}

// 合同状态枚举
export enum ContractStatus {
  DRAFT = '草稿',
  PENDING = '待审核',
  APPROVED = '已通过',
  REJECTED = '已拒绝',
  SIGNED = '已签约'
}

// 服务场景树形结构
export interface ServiceScenario {
  id: string;
  name: string;
  count: number;
  children?: ServiceScenario[];
}

// 运营服务项
export interface OperationServiceItem {
  id: string;
  type: string;        // 运营服务类型 (如: 集成办服务)
  period: string;      // 运营服务期间 (如: XXXXXXXX)
  description: string; // 业务办理项 (如: 主题)
  quantity: number;    // 数量
  unit: string;        // 单位 (如: 个)
  isEditable: boolean; // 数量是否可编辑
}

// 产品信息
export interface Product {
  id: string;
  name: string;
  description: string;
  isRequired: boolean; // 是否为必选产品
  isSelected: boolean; // 是否已选中
  operationServices: OperationServiceItem[]; // 运营服务清单
  totalCount: number; // 总计数量
  hasServices: boolean; // 是否有运营服务
}

// 商机项目数据
export interface BusinessOpportunityItem {
  id: string;
  projectName: string; // 产品项目名称
  operationServiceContent: string; // 运营服务内容
  measurementMethod: string; // 计量方式
  quantity: number; // 数量
  buildStatus: string; // 商机项目建设状态
  productId: string; // 关联的产品ID
  serviceId: string; // 关联的服务ID
}

// 产品选择组件状态
export interface ProductSelectionState {
  selectedScenarioId: string | null;
  products: Product[];
  businessOpportunityItems: BusinessOpportunityItem[];
  serviceScenarios: ServiceScenario[];
}

// 产品选择组件Props
export interface ProductSelectionProps {
  onBusinessOpportunityChange?: (items: BusinessOpportunityItem[]) => void;
}

// 合同数据接口
export interface ContractItem {
  id: string;
  序号: number;
  合同编号: string;
  合同名称: string;
  所属商机项目: string;
  合同类型: string;
  合同税率: string;
  合同签约金额: number;
  合同收入金额: number;
  甲方名称: string;
  商机项目是否超出合同约定部分: string;
  销售负责人: string;
  合同状态: string;
}

// 合同管理组件Props
export interface ContractManagementProps {
  // 数据相关
  incomeContracts: ContractItem[];
  costContracts: ContractItem[];
  loading: boolean;
  error?: string;
  
  // 分页相关
  currentPage: number;
  pageSize: number;
  total: number;
  
  // 回调函数
  onTabChange: (activeKey: string) => void;
  onPageChange: (page: number, size?: number) => void;
  onView: (contractId: string) => void;
  onConfirmSign: (contractId: string) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
}

// Tab键枚举
export enum TabKey {
  INCOME = 'income',
  COST = 'cost'
}

// 表格列配置
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (text: any, record: ContractItem, index: number) => React.ReactNode;
}

// 分阶段回款项
export interface StagePaymentItem {
  id: string;
  阶段名称: string;
  合同内容: string;
  回款比例: string;
  交付条件: string;
  实施内容和交付成果: string;
}

// 项目回款明细项
export interface PaymentDetailItem {
  id: string;
  回款时间: string;
  回款金额: number;
  备注: string;
}

// 合同详情数据接口
export interface ContractDetailData extends ContractItem {
  // 合同周期信息
  合同开始日期?: string;
  合同结束日期?: string;
  结算方式?: string;
  交付条件?: string;
  
  // 分阶段回款
  分阶段回款列表: StagePaymentItem[];
  
  // 项目回款明细
  项目回款明细列表: PaymentDetailItem[];
  
  // 甲方信息
  甲方信息: {
    甲方名称: string;
    甲方联系人: string;
    甲方联系人电话: string;
    甲方联系人邮箱: string;
  };
  
  // 乙方信息
  乙方信息: {
    乙方名称: string;
    乙方联系人: string;
    乙方联系人电话: string;
    乙方联系人邮箱: string;
  };
  
  // 知识产权及其他条款
  知识产权及其他条款?: string;
  
  // 违约条款及违约
  违约条款及违约?: string;
}

// 合同详情页面Props
export interface ContractDetailProps {
  contractData: ContractDetailData | null;
  loading: boolean;
  error?: string;
  onBack?: () => void;
  onEdit?: (contractId: string) => void;
  onRefresh?: () => void;
} 