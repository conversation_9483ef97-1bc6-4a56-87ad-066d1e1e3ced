import React, { useState, useCallback } from 'react';
import { Tabs } from 'antd';
import Contract from './components/Contract';
import Cascade from './components/Cascade';
import { ContractItem, TabKey } from './types';

const { TabPane } = Tabs;

/**
 * 合同管理组件使用示例
 * 展示如何正确使用ContractManagement组件
 */
const ContractManagement: React.FC = () => {
  // 模拟数据
  const mockIncomeContracts: ContractItem[] = [
    {
      id: '1',
      序号: 1,
      合同编号: 'HT2025051800TSC',
      合同名称: '2025年四川省大府易享享数据平台合同',
      所属商机项目: '2025年四川省大府易享享数据平台商机项目',
      合同类型: '开发合同',
      合同税率: '13%',
      合同签约金额: 4.000,
      合同收入金额: 3539.82,
      甲方名称: '成都大数据中心',
      商机项目是否超出合同约定部分: '否',
      销售负责人: '李明',
      合同状态: '通过评审'
    },
    {
      id: '2',
      序号: 2,
      合同编号: 'HT2025051800TSC',
      合同名称: '2025年四川省大府易享享数据平台合同',
      所属商机项目: '2025年四川省大府易享享数据平台商机项目',
      合同类型: '开发合同',
      合同税率: '13%',
      合同签约金额: 4.000,
      合同收入金额: 3539.82,
      甲方名称: '成都大数据中心',
      商机项目是否超出合同约定部分: '否',
      销售负责人: '李明',
      合同状态: '确认签约'
    }
  ];

  const mockCostContracts: ContractItem[] = [];

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState('product-selection');

  // 回调函数实现
  const handleTabChange = useCallback((tabKey: string) => {
    console.log('Tab changed to:', tabKey);
    setActiveTab(tabKey);
    setCurrentPage(1); // 切换tab时重置页码
  }, []);

  const handlePageChange = useCallback((page: number, size?: number) => {
    console.log('Page changed:', page, size);
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
    // 这里应该触发数据重新加载
  }, []);

  const handleView = useCallback((contractId: string) => {
    console.log('View contract:', contractId);
    // 这里应该跳转到合同详情页面
  }, []);

  const handleConfirmSign = useCallback((contractId: string) => {
    console.log('Confirm sign contract:', contractId);
    // 这里应该调用确认签约API
  }, []);

  const handleSearch = useCallback((searchText: string) => {
    console.log('Search:', searchText);
    // 这里应该触发搜索
  }, []);

  const handleRefresh = useCallback(() => {
    console.log('Refresh data');
    setLoading(true);
    // 模拟数据加载
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  return (
    <div className="p-6">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        size="large"
        className="bg-white rounded-lg shadow-sm"
      >
        <TabPane tab="产品选择" key="product-selection">
          <Cascade />
        </TabPane>
        <TabPane tab="合同管理" key="contract-management">
          <Contract
            incomeContracts={mockIncomeContracts}
            costContracts={mockCostContracts}
            loading={loading}
            currentPage={currentPage}
            pageSize={pageSize}
            total={mockIncomeContracts.length}
            onTabChange={handleTabChange}
            onPageChange={handlePageChange}
            onView={handleView}
            onConfirmSign={handleConfirmSign}
            onSearch={handleSearch}
            onRefresh={handleRefresh}
          />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ContractManagement; 