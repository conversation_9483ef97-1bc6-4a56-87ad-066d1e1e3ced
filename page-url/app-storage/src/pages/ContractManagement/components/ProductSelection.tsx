import React, { useState, useCallback, useEffect } from 'react';
import { Row, Col, Card, Tree, Checkbox, Table, InputNumber, Empty } from 'antd';
import type { 
  ServiceScenario, 
  Product, 
  BusinessOpportunityItem, 
  OperationServiceItem,
  ProductSelectionProps 
} from '../types';

// 模拟服务场景数据
const mockServiceScenarios: ServiceScenario[] = [
  {
    id: '1',
    name: '好办一件事',
    count: 8,
    children: []
  },
  {
    id: '2',
    name: '智能服务中枢',
    count: 2,
    children: []
  },
  {
    id: '3',
    name: '事项梳理',
    count: 1,
    children: []
  },
  {
    id: '4',
    name: '办事指南校验',
    count: 1,
    children: []
  },
  {
    id: '5',
    name: '材料预审',
    count: 4,
    children: []
  },
  {
    id: '6',
    name: '政策平台',
    count: 4,
    children: []
  },
  {
    id: '7',
    name: '数据上报',
    count: 4,
    children: []
  },
  {
    id: '8',
    name: '历史材料治理',
    count: 4,
    children: []
  },
  {
    id: '9',
    name: '电子材料库',
    count: 4,
    children: []
  },
  {
    id: '10',
    name: '数字政务门牌（政务地图）',
    count: 4,
    children: []
  },
  {
    id: '11',
    name: '用户专属空间',
    count: 4,
    children: []
  },
  {
    id: '12',
    name: '政务服务专区（含一类事）',
    count: 4,
    children: []
  },
  {
    id: '13',
    name: '智能客服（含极简办）',
    count: 4,
    children: []
  },
  {
    id: '14',
    name: '智能搜索',
    count: 4,
    children: []
  },
  {
    id: '15',
    name: '消息中心',
    count: 4,
    children: []
  },
  {
    id: '16',
    name: '发码验码',
    count: 1,
    children: []
  },
  {
    id: '17',
    name: '码场景拓展',
    count: 1,
    children: []
  },
  {
    id: '18',
    name: '智能统筹辅助（规划中）',
    count: 1,
    children: []
  }
];

// 模拟产品数据
const mockProducts: Product[] = [
  {
    id: 'product-1',
    name: '智能构建系统/智能服务运营系统',
    description: '"好办"部"一件事"是近年来政府政务服务領域推出的便民服务产品，旨在通过整合各部门、多事项，实现"一次申请、一套材料、一窗通办、一网通办"的一体化办理模式，为企业和群众提供高品质、便捷的政务服务。',
    isRequired: true,
    isSelected: false,
    hasServices: true,
    totalCount: 24,
    operationServices: [
      {
        id: 'service-1-1',
        type: '集成办服务',
        period: 'XXXXXXXX',
        description: '主题',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-2',
        type: '跨域办服务',
        period: 'XXXXXXXX',
        description: '事项',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-3',
        type: '免申办服务',
        period: 'XXXXXXXX',
        description: '政策',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-4',
        type: '承诺办服务',
        period: 'XXXXXXXX',
        description: '事项',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-5',
        type: '一类事一站办服务',
        period: 'XXXXXXXX',
        description: '主题',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-6',
        type: '一业一证服务',
        period: 'XXXXXXXX',
        description: '主题',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-7',
        type: '智慧好办服务',
        period: 'XXXXXXXX',
        description: '事项',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-1-8',
        type: '政策申报服务',
        period: 'XXXXXXXX',
        description: '申报项目',
        quantity: 3,
        unit: '个',
        isEditable: true
      }
    ]
  },
  {
    id: 'product-2',
    name: '用户行为洞察系统',
    description: '用户行为洞察这套应用户在政务服务平台上的行为数据，构建用户行为画像，支持精细化服务推荐，智能分析与产品优化决策，推动政务服务从"可用"向"好用、易用、智能化"演进。',
    isRequired: false,
    isSelected: false,
    hasServices: false,
    totalCount: 0,
    operationServices: []
  },
  {
    id: 'product-3',
    name: '智能问询系统',
    description: '智能问询系统构建政务多文档支撑之间的知识图谱，融合政策、事项、材料、审批等多维度关系，支持语义理解与智能检索，提升智能客服、审批指南与业务分析场景的智能服务能力。',
    isRequired: false,
    isSelected: false,
    hasServices: true,
    totalCount: 9,
    operationServices: [
      {
        id: 'service-3-1',
        type: '事项数智标编服务',
        period: 'XXXXXXXX',
        description: '业务办理项',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-3-2',
        type: '办事指南校验服务',
        period: 'XXXXXXXX',
        description: '业务办理项',
        quantity: 3,
        unit: '个',
        isEditable: true
      },
      {
        id: 'service-3-3',
        type: '办事指南横向对比服务',
        period: 'XXXXXXXX',
        description: '事项',
        quantity: 3,
        unit: '个',
        isEditable: true
      }
    ]
  }
];

const ProductSelection: React.FC<ProductSelectionProps> = ({ onBusinessOpportunityChange }) => {
  const [selectedScenarioId, setSelectedScenarioId] = useState<string | null>('1');
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [businessOpportunityItems, setBusinessOpportunityItems] = useState<BusinessOpportunityItem[]>([]);

  // 处理服务场景选择
  const handleScenarioSelect = useCallback((selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      setSelectedScenarioId(selectedKeys[0] as string);
    }
  }, []);

  // 处理产品勾选/取消
  const handleProductSelect = useCallback((productId: string, checked: boolean) => {
    setProducts(prevProducts => 
      prevProducts.map(product => {
        if (product.id === productId) {
          // 必选产品不允许取消勾选
          if (product.isRequired && !checked) {
            return product;
          }
          
          const updatedProduct = { ...product, isSelected: checked };
          
          // 同步更新商机项目列表
          if (checked && product.hasServices) {
            // 添加到商机项目列表
            const newItems = product.operationServices.map(service => ({
              id: `${productId}-${service.id}`,
              projectName: `2024年"高效办成一件事"智能构建系统V2.0产品项目`,
              operationServiceContent: service.type,
              measurementMethod: `${service.description}数量（${service.unit}）`,
              quantity: service.quantity,
              buildStatus: '未建设',
              productId: product.id,
              serviceId: service.id
            }));
            
            setBusinessOpportunityItems(prev => [...prev, ...newItems]);
          } else if (!checked) {
            // 从商机项目列表中移除
            setBusinessOpportunityItems(prev => 
              prev.filter(item => item.productId !== productId)
            );
          }
          
          return updatedProduct;
        }
        return product;
      })
    );
  }, []);

  // 处理商机项目数量变更
  const handleQuantityChange = useCallback((itemId: string, newQuantity: number) => {
    setBusinessOpportunityItems(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  }, []);

  // 商机项目表格列配置
  const businessOpportunityColumns = [
    {
      title: '产品项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 300,
    },
    {
      title: '运营服务内容',
      dataIndex: 'operationServiceContent',
      key: 'operationServiceContent',
      width: 150,
    },
    {
      title: '计量方式',
      dataIndex: 'measurementMethod',
      key: 'measurementMethod',
      width: 180,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (quantity: number, record: BusinessOpportunityItem) => (
        <InputNumber
          min={1}
          value={quantity}
          onChange={(value) => handleQuantityChange(record.id, value || 1)}
          className="w-full"
        />
      ),
    },
    {
      title: '商机项目建设状态',
      dataIndex: 'buildStatus',
      key: 'buildStatus',
      width: 150,
    },
  ];

  // 服务场景树形数据转换
  const treeData = mockServiceScenarios.map(scenario => ({
    title: `${scenario.name} (${scenario.count})`,
    key: scenario.id,
    icon: <span className="text-blue-500">📁</span>,
  }));

  // 运营服务清单表格列配置
  const operationServiceColumns = [
    {
      title: '运营服务类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: '运营服务期间',
      dataIndex: 'period',
      key: 'period',
      width: 120,
    },
    {
      title: '业务办理项',
      dataIndex: 'description',
      key: 'description',
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (quantity: number, record: OperationServiceItem) => (
        <span>{quantity} {record.unit}</span>
      ),
    },
  ];

  // 通知父组件商机项目变更
  useEffect(() => {
    onBusinessOpportunityChange?.(businessOpportunityItems);
  }, [businessOpportunityItems, onBusinessOpportunityChange]);

  // 默认展开第一个服务场景
  useEffect(() => {
    if (mockServiceScenarios.length > 0) {
      setSelectedScenarioId('1');
    }
  }, []);

  return (
    <div className="bg-white">
      {/* 选择服务场景和选择对应产品 */}
      <div className="p-3 mb-4 text-white bg-blue-500">
        <Row>
          <Col span={12}>
            <div className="text-lg font-medium text-center">选择服务场景</div>
          </Col>
          <Col span={12}>
            <div className="text-lg font-medium text-center">选择对应产品</div>
          </Col>
        </Row>
      </div>

      <Row gutter={16} className="mb-6">
        {/* 左侧：服务场景选择 */}
        <Col span={12}>
          <Card className="overflow-auto h-96">
            <Tree
              treeData={treeData}
              selectedKeys={selectedScenarioId ? [selectedScenarioId] : []}
              onSelect={handleScenarioSelect}
              showIcon
              className="text-sm"
            />
          </Card>
        </Col>

        {/* 右侧：产品选择 */}
        <Col span={12}>
          <Card className="overflow-auto h-96">
            <div className="space-y-4">
              {products.map(product => (
                <div key={product.id} className="p-4 rounded-lg border">
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      checked={product.isSelected}
                      disabled={product.isRequired && product.isSelected}
                      onChange={(e) => handleProductSelect(product.id, e.target.checked)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="mb-2 font-medium text-gray-900">
                        {product.name}
                        {product.isRequired && (
                          <span className="ml-2 text-xs text-red-500">【必选产品】</span>
                        )}
                      </div>
                      <div className="mb-3 text-sm leading-relaxed text-gray-600">
                        {product.description}
                      </div>
                      
                      {product.hasServices ? (
                        <div>
                          <div className="mb-2 text-sm font-medium text-blue-600">
                            📊 运营服务清单 总计: {product.totalCount}
                          </div>
                          <Table
                            dataSource={product.operationServices}
                            columns={operationServiceColumns}
                            pagination={false}
                            size="small"
                            className="rounded border"
                            rowKey="id"
                          />
                        </div>
                      ) : (
                        <div className="py-8 text-center">
                          <Empty 
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                            description="暂无运营服务"
                            className="text-gray-400"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 底部：商机项目建设清单 */}
      {businessOpportunityItems.length > 0 && (
        <Card title="商机项目建设清单" className="mt-6">
          <Table
            dataSource={businessOpportunityItems}
            columns={businessOpportunityColumns}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            rowKey="id"
            scroll={{ x: 1000 }}
          />
        </Card>
      )}
    </div>
  );
};

export default ProductSelection; 