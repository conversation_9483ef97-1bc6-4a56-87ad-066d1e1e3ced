import React, { useState, useCallback } from 'react';
import { Card, message } from 'antd';
import ProductSelection from './ProductSelection';
import type { BusinessOpportunityItem } from '../types';

/**
 * 级联选择组件
 * 实现产品选择和商机项目管理的完整功能
 */
const Cascade: React.FC = () => {
  const [businessOpportunityItems, setBusinessOpportunityItems] = useState<BusinessOpportunityItem[]>([]);

  // 处理商机项目数据变更
  const handleBusinessOpportunityChange = useCallback((items: BusinessOpportunityItem[]) => {
    setBusinessOpportunityItems(items);
    
    // 可以在这里添加数据同步逻辑，比如保存到服务器
    if (items.length > 0) {
      message.success(`已选择 ${items.length} 个商机项目`);
    }
  }, []);

  return (
    <div className="p-6 min-h-screen bg-gray-50">
      <Card
        title="产品选择与商机项目管理"
        className="mb-6"
        extra={
          <div className="text-gray-600">
            已选择商机项目: <span className="font-medium text-blue-600">{businessOpportunityItems.length}</span> 个
          </div>
        }
      >
        <ProductSelection onBusinessOpportunityChange={handleBusinessOpportunityChange} />
      </Card>

      {/* 可以在这里添加其他相关功能 */}
      {businessOpportunityItems.length > 0 && (
        <Card title="选择摘要" size="small">
          <div className="space-y-2">
            <div className="text-sm text-gray-600">
              <span className="font-medium">商机项目总数:</span> {businessOpportunityItems.length} 个
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">总计量:</span> {businessOpportunityItems.reduce((sum, item) => sum + item.quantity, 0)} 
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">涉及产品:</span> {new Set(businessOpportunityItems.map(item => item.productId)).size} 个
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Cascade;