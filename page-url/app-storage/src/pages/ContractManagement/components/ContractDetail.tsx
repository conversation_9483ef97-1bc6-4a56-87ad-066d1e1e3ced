import React, { useMemo } from 'react';
import { Card, Descriptions, Table, Button, Space, Tag, Spin } from 'antd';
import { ArrowLeftOutlined, EditOutlined, ReloadOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { ContractDetailProps, StagePaymentItem, PaymentDetailItem } from '../types';

/**
 * 合同详情组件
 * 展示完整的合同信息，包括基本信息、合同信息、分阶段回款、项目回款明细等
 */
const ContractDetail: React.FC<ContractDetailProps> = React.memo(({
  contractData,
  loading,
  error,
  onBack,
  onEdit,
  onRefresh
}) => {
  // 分阶段回款表格列配置
  const stagePaymentColumns: ColumnsType<StagePaymentItem> = useMemo(() => [
    {
      title: '阶段名称',
      dataIndex: '阶段名称',
      key: 'stageName',
      width: 120,
    },
    {
      title: '合同内容',
      dataIndex: '合同内容',
      key: 'contractContent',
      width: 200,
      ellipsis: true,
    },
    {
      title: '回款比例',
      dataIndex: '回款比例',
      key: 'paymentRatio',
      width: 100,
      align: 'center',
    },
    {
      title: '交付条件',
      dataIndex: '交付条件',
      key: 'deliveryCondition',
      width: 150,
      ellipsis: true,
    },
    {
      title: '实施内容和交付成果',
      dataIndex: '实施内容和交付成果',
      key: 'implementationContent',
      ellipsis: true,
    },
  ], []);

  // 项目回款明细表格列配置
  const paymentDetailColumns: ColumnsType<PaymentDetailItem> = useMemo(() => [
    {
      title: '回款时间',
      dataIndex: '回款时间',
      key: 'paymentTime',
      width: 120,
      align: 'center',
    },
    {
      title: '回款金额(万元)',
      dataIndex: '回款金额',
      key: 'paymentAmount',
      width: 150,
      align: 'right',
      render: (amount: number) => amount?.toFixed(2) || '0.00',
    },
    {
      title: '备注',
      dataIndex: '备注',
      key: 'remark',
      ellipsis: true,
    },
  ], []);

  // 渲染合同状态标签
  const renderStatus = (status: string) => {
    const statusColors: Record<string, string> = {
      '草稿': 'default',
      '待审核': 'processing',
      '已通过': 'success',
      '已拒绝': 'error',
      '已签约': 'success',
      '通过评审': 'success',
      '确认签约': 'success'
    };

    return (
      <Tag color={statusColors[status] || 'default'}>
        {status}
      </Tag>
    );
  };

  // 错误状态处理
  if (error) {
    return (
      <Card className="m-6">
        <div className="py-8 text-center text-red-500">
          <p>加载失败: {error}</p>
          {onRefresh && (
            <Button type="primary" onClick={onRefresh} className="mt-4">
              重新加载
            </Button>
          )}
        </div>
      </Card>
    );
  }

  // 数据为空时的处理
  if (!contractData) {
    return (
      <div className="p-6 min-h-screen bg-gray-50">
        <Spin spinning={loading}>
          <div className="flex justify-center items-center min-h-96">
            <div className="text-center">
              <p className="mb-4 text-gray-500">正在加载合同信息...</p>
              {onBack && (
                <Button onClick={onBack}>
                  返回列表
                </Button>
              )}
            </div>
          </div>
        </Spin>
      </div>
    );
  }

    return (
    <div className="min-h-screen bg-white">
      <Spin spinning={loading}>
        {/* 页面头部 */}
        <div className="p-6 bg-white border-b border-gray-200">
          <Space size="large">
            {onBack && (
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={onBack}
                className="flex items-center"
              >
                返回
              </Button>
            )}
            <h2 className="m-0 text-xl font-semibold">合同详情基础</h2>
          </Space>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
        {/* 合同基本信息 */}
        <Card title="合同基本信息" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="合同名称">
              {contractData.合同名称}
            </Descriptions.Item>
            <Descriptions.Item label="合同编号">
              {contractData.合同编号}
            </Descriptions.Item>
            <Descriptions.Item label="所属商机项目">
              {contractData.所属商机项目}
            </Descriptions.Item>
            <Descriptions.Item label="所属项目小组成员">
              {contractData.所属商机项目}团队
            </Descriptions.Item>
            <Descriptions.Item label="合同类型">
              {contractData.合同类型}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 合同信息 */}
        <Card title="合同信息" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="合同税率">
              {contractData.合同税率}
            </Descriptions.Item>
            <Descriptions.Item label="合同签约金额(万元)">
              {contractData.合同签约金额?.toFixed(3) || '0.000'}
            </Descriptions.Item>
            <Descriptions.Item label="签约最高金额(万元)">
              {contractData.合同签约金额?.toFixed(3) || '0.000'}
            </Descriptions.Item>
            <Descriptions.Item label="合同收入金额(万元)">
              {contractData.合同收入金额?.toFixed(2) || '0.00'}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 合同周期 */}
        <Card title="合同周期" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="整体周期">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <span className="w-20 text-gray-600">合同开始：</span>
                  <span className="flex-1 px-2 py-1 border-b border-gray-300">
                    {contractData.合同开始日期 || ''}
                  </span>
                </div>
                <div className="flex items-center">
                  <span className="w-12 text-gray-600">至</span>
                  <span className="flex-1 px-2 py-1 border-b border-gray-300">
                    {contractData.合同结束日期 || ''}
                  </span>
                </div>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="合同期间">
              <div className="grid grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="mb-2 text-xs text-gray-500">开始日期</div>
                  <div className="py-1 border-b border-gray-300">
                    {contractData.合同开始日期 || ''}
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-xs text-gray-500">结束日期</div>
                  <div className="py-1 border-b border-gray-300">
                    {contractData.合同结束日期 || ''}
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-xs text-gray-500">结算方式</div>
                  <div className="py-1 border-b border-gray-300">
                    {contractData.结算方式 || ''}
                  </div>
                </div>
                <div className="text-center">
                  <div className="mb-2 text-xs text-gray-500">交付条件</div>
                  <div className="py-1 border-b border-gray-300">
                    {contractData.交付条件 || ''}
                  </div>
                </div>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="行款条件">
              <div className="grid grid-cols-4 gap-2 text-center">
                <div className="p-2 bg-white rounded border border-gray-300">
                  <div className="mb-1 text-xs text-gray-500">付款方式</div>
                  <div className="text-xs">{contractData.结算方式 || ''}</div>
                </div>
                <div className="p-2 bg-white rounded border border-gray-300">
                  <div className="mb-1 text-xs text-gray-500">付款比例</div>
                  <div className="text-xs">60%</div>
                </div>
                <div className="p-2 bg-white rounded border border-gray-300">
                  <div className="mb-1 text-xs text-gray-500">付款时间</div>
                  <div className="text-xs">按阶段付款</div>
                </div>
                <div className="p-2 bg-white rounded border border-gray-300">
                  <div className="mb-1 text-xs text-gray-500">交付条件</div>
                  <div className="text-xs">{contractData.交付条件 || ''}</div>
                </div>
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 分阶段回款 */}
        <Card title="分阶段回款" className="mb-6">
          <Table<StagePaymentItem>
            columns={stagePaymentColumns}
            dataSource={contractData.分阶段回款列表}
            rowKey="id"
            pagination={false}
            size="small"
            bordered
            scroll={{ x: 800 }}
          />
        </Card>

        {/* 项目回款明细 */}
        <Card title="项目回款明细" className="mb-6">
          <Table<PaymentDetailItem>
            columns={paymentDetailColumns}
            dataSource={contractData.项目回款明细列表}
            rowKey="id"
            pagination={false}
            size="small"
            bordered
          />
        </Card>

        {/* 甲方信息 */}
        <Card title="甲方信息" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="甲方名称">
              {contractData.甲方信息.甲方名称}
            </Descriptions.Item>
            <Descriptions.Item label="甲方联系人">
              {contractData.甲方信息.甲方联系人}
            </Descriptions.Item>
            <Descriptions.Item label="甲方联系人电话">
              {contractData.甲方信息.甲方联系人电话}
            </Descriptions.Item>
            <Descriptions.Item label="甲方联系人邮箱">
              {contractData.甲方信息.甲方联系人邮箱}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 乙方信息 */}
        <Card title="乙方信息" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="乙方名称">
              {contractData.乙方信息.乙方名称}
            </Descriptions.Item>
            <Descriptions.Item label="乙方联系人">
              {contractData.乙方信息.乙方联系人}
            </Descriptions.Item>
            <Descriptions.Item label="乙方联系人电话">
              {contractData.乙方信息.乙方联系人电话}
            </Descriptions.Item>
            <Descriptions.Item label="乙方联系人邮箱">
              {contractData.乙方信息.乙方联系人邮箱}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 知识产权及其他条款 */}
        <Card title="知识产权及其他条款" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="">
              <div className="whitespace-pre-wrap">
                {contractData.知识产权及其他条款 || '本产权归甲方享有协议开发成果应充满办技术方案的全部知识产权，乙方在提案服务过程中，使用甲方现有资源的，乙方应当严格保密，不得用于其他场所。\n0000版。'}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 违约条款及违约 */}
        <Card title="违约条款及违约" className="mb-6">
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="">
              <div className="whitespace-pre-wrap">
                {contractData.违约条款及违约 || '如果一方未能按照约定履行本合同义务，视方须进行相应的赔付工作，受方有权要求违约方当时相应的违约责任。各项涉及的违约责任，合同双方将在合作前详细约定。\n0000版。'}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 产品服务 */}
        <Card title="产品服务" className="mb-6">
          <Table
            dataSource={[
              {
                key: '1',
                description: '企业项目管理系统开发',
                unit: '套',
                quantity: 1,
                unitPrice: 350000.00,
                totalPrice: 350000.00,
                notes: '包含需求分析、系统设计、开发、测试及部署'
              },
              {
                key: '2',
                description: '系统维护服务',
                unit: '年',
                quantity: 2,
                unitPrice: 50000.00,
                totalPrice: 100000.00,
                notes: '为期两年的系统维护和技术支持'
              }
            ]}
            columns={[
              {
                title: '产品/服务描述',
                dataIndex: 'description',
                key: 'description',
                width: 200,
              },
              {
                title: '单位',
                dataIndex: 'unit',
                key: 'unit',
                width: 80,
                align: 'center',
              },
              {
                title: '数量',
                dataIndex: 'quantity',
                key: 'quantity',
                width: 80,
                align: 'center',
              },
              {
                title: '单价(元)',
                dataIndex: 'unitPrice',
                key: 'unitPrice',
                width: 120,
                align: 'right',
                render: (value: number) => value.toLocaleString(),
              },
              {
                title: '总价(元)',
                dataIndex: 'totalPrice',
                key: 'totalPrice',
                width: 120,
                align: 'right',
                render: (value: number) => value.toLocaleString(),
              },
              {
                title: '备注',
                dataIndex: 'notes',
                key: 'notes',
              },
            ]}
            pagination={false}
            size="small"
            bordered
          />
        </Card>
        </div>
      </Spin>
    </div>
  );
});

ContractDetail.displayName = 'ContractDetail';

export default ContractDetail; 