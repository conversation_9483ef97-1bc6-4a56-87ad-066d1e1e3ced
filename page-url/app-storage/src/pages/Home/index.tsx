import React from "react";
import { Input, Button, Card, Space, Switch, Select, Typography } from "antd";
import MasterContext from "@/context/masterContext";
import { useCounterStore, useUserStore, useTokenIDStore } from "@/store";
import { usePersistStore, useSessionStore } from "@/store/persistStore";
import styles from "./index.less";
import { Link } from "react-router-dom";
import { cpapi } from "@clickpaas/cp-api";

const { Title, Text } = Typography;
const { Option } = Select;

const Home = () => {
  /** APaaS 平台传递给微应用的状态数据 */
  const props = React.useContext(MasterContext);
  /** 当组件运行在 APaaS 基座应用时才存在 props 属性 */
  const { configurations } = props || {};
  /** configurations 是组件的配置项信息 */

  // 使用计数器 store
  const { count, increment, decrement, reset } = useCounterStore();
  const { tokenID, setTokenID } = useTokenIDStore();
  // 使用用户 store
  const { user, setUser, clearUser } = useUserStore();

  // 使用持久化 store
  const { theme, language, settings, setTheme, setLanguage, updateSettings } =
    usePersistStore();

  // 使用会话 store
  const {
    currentPage,
    breadcrumbs,
    setCurrentPage,
    addBreadcrumb,
    clearBreadcrumbs,
  } = useSessionStore();

  // 模拟设置用户信息
  const handleSetUser = () => {
    setUser({
      id: "1",
      name: "张三",
      email: "<EMAIL>",
    });
  };

  const getUserInfo = async() => {
    console.log("获取用户信息=================");
    const res = await cpapi.common.getUserInfo({ 
      otherWindow: window.parent, 
      data: {
      }
    })
    console.log("获取用户信息res=================", res);
  }

  return (
    <div className='p-4' >
      <header className="mb-6">
        <Link to="/">首页</Link> |<Link to="/new">加班数据分析</Link> |
        <Link to="/list">列表</Link> |<Link to="/view">详情</Link>
        |<Link to="/implementation-costs">实施成本</Link>
        |<Link to="/work-hours-statistics">工时统计</Link>
        |<Link to="/contract-management">合同管理</Link>
      </header>
     <Space>
     <Button type="primary" onClick={getUserInfo}>获取用户信息</Button>
     </Space>
      <Space>
        <Input
          placeholder="设置tokenID"
          value={tokenID}
          onChange={(e) => setTokenID(e.target.value)}
        />
        <Button
          type="primary"
          onClick={() => (document.cookie = `tokenId=${tokenID}; _lang=zh_CN`)}
        >
          设置cookie
        </Button>
      </Space>
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        {/* 计数器示例 */}
        <h3>增加测试看看有没有缓存</h3>
        <Card title="计数器状态管理" size="small">
          <Space>
            <Text>当前计数: {count}</Text>
            <Button onClick={increment}>增加</Button>
            <Button onClick={decrement}>减少</Button>
            <Button onClick={reset}>重置</Button>
          </Space>
        </Card>

        {/* 用户信息示例 */}
        <Card title="用户信息管理" size="small">
          {user ? (
            <Space direction="vertical">
              <Text>用户ID: {user.id}</Text>
              <Text>姓名: {user.name}</Text>
              <Text>邮箱: {user.email}</Text>
              <Button onClick={clearUser}>清除用户</Button>
            </Space>
          ) : (
            <Space>
              <Text>暂无用户信息</Text>
              <Button onClick={handleSetUser}>设置用户</Button>
            </Space>
          )}
        </Card>

        {/* 持久化设置示例 */}
        <Card title="持久化设置 (localStorage)" size="small">
          <Space direction="vertical" style={{ width: "100%" }}>
            <Space>
              <Text>主题:</Text>
              <Select value={theme} onChange={setTheme} style={{ width: 120 }}>
                <Option value="light">浅色</Option>
                <Option value="dark">深色</Option>
              </Select>
            </Space>
            <Space>
              <Text>语言:</Text>
              <Select
                value={language}
                onChange={setLanguage}
                style={{ width: 120 }}
              >
                <Option value="zh">中文</Option>
                <Option value="en">English</Option>
              </Select>
            </Space>
            <Space>
              <Text>通知:</Text>
              <Switch
                checked={settings.notifications}
                onChange={(checked) =>
                  updateSettings({ notifications: checked })
                }
              />
            </Space>
            <Space>
              <Text>自动保存:</Text>
              <Switch
                checked={settings.autoSave}
                onChange={(checked) => updateSettings({ autoSave: checked })}
              />
            </Space>
          </Space>
        </Card>

        {/* 会话状态示例 */}
        <Card title="会话状态管理 (sessionStorage)" size="small">
          <Space direction="vertical" style={{ width: "100%" }}>
            <Space>
              <Text>当前页面: {currentPage}</Text>
              <Button onClick={() => setCurrentPage("list")}>
                切换到列表页
              </Button>
              <Button onClick={() => setCurrentPage("detail")}>
                切换到详情页
              </Button>
            </Space>
            <Space>
              <Text>面包屑: {breadcrumbs.join(" > ") || "无"}</Text>
              <Button onClick={() => addBreadcrumb("新页面")}>
                添加面包屑
              </Button>
              <Button onClick={clearBreadcrumbs}>清空面包屑</Button>
            </Space>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default Home;
