import React from "react";
import { createHashRouter } from "react-router-dom";
import BasicLayout from "@/layouts/BasicLayout";
import NoMatch from "@/components/NoMatch";
import List from "@/pages/List";
import View from "@/pages/View";
import Home from "@/pages/Home";
import ImplementationCosts from "@/pages/ImplementationCosts";
import Readonly from "./pages/ImplementationCosts/readonly";
import New from "./pages/New";
import WorkHoursStatistics from "./pages/WorkHoursStatistics";
import ContractManagement from "./pages/ContractManagement/page";
import ContractDetailPage from "./pages/ContractManagement/detail";

const initRouter = (basename?: string) => {
  return createHashRouter(
    [
      {
        path: "/",
        element: <BasicLayout />,
        children: [
          {
            index: true,
            element: <Home />,
          },
          {
            path: "/new",
            element: <New />,
          },
          {
            path: "/list",
            element: <List />,
          },
          {
            path: "/view",
            element: <View />,
          },
          {
            // 实施成本
            path: "/implementation-costs",
            element: <ImplementationCosts />,
          },
          {
            // 实施成本-只读
            path: "/implementation-costs-readonly",
            element: <Readonly />,
          },
          {
            // 工时统计
            path: "/work-hours-statistics",
            element: <WorkHoursStatistics />,
          },
          {
            // 合同管理
            path: "/contract-management",
            element: <ContractManagement />,
          },
          {
            // 合同详情
            path: "/contract-management/:contractId",
            element: <ContractDetailPage />,
          },
        ],
      },
      {
        path: "*",
        element: <NoMatch />,
      },
    ],
    {
      basename,
    }
  );
};

export default initRouter;
