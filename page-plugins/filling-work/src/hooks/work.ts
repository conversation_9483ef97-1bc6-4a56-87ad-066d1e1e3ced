import { useMemo } from 'react';
import { StatusInfo, TaskHourDetail, WorkTimeConstants } from '../interface';

export const workProjectType = {
    product: {
      name: "产品交付小组",
      type: "研发工时",
    },
    business: {
      name: "商机交付小组",
      type: "交付工时",
    },
  };
// 工时常量配置
export const useWorkTimeConstants = (): WorkTimeConstants => {
  return useMemo(() => ({
    workType: ["HW2376", "HW2377"],
    type: ["班", "休"],
    statusInfo: {
      completed: {
        backgroundColor: "#5378ff",
        click: "pointer",
      },
      info: {
        backgroundColor: "#d7d7d7",
      },
      add: {
        backgroundColor: "#5378ff",
        click: "pointer",
      },
      pending: {
        backgroundColor: "#F59A23",
        backRaund: "#FEF9F2",
        title: "审核中",
        tip: "当前工时审核中",
      },
      error: {
        backgroundColor: "#FF4D4F",
        backRaund: "#FFF2F0",
        title: "驳回",
        click: "pointer",
      },
    }
  }), []);
};

// 获取状态信息的hook
export const useStatusInfo = () => {
  const { workType, statusInfo } = useWorkTimeConstants();

  const getStatusInfo = (item: { 
    id?: string | null; 
    hour?: string | number; 
    checkStatus?: string | null; 
    isSent?: boolean; 
  }, typeName?: string): StatusInfo => {
    let info: StatusInfo = statusInfo.info;
    const hourStatus = item.checkStatus;

    if (item.id) {
      info = statusInfo.info;
    }
    if (!item.id) {
      info = statusInfo.add;
    }
    if (item.isSent) {
      info = statusInfo.completed;
    }
    if (!item.id && item.hour) {
      info = statusInfo.completed;
    }
    if (typeName === "休") {
      info = statusInfo.info;
    }
    if (hourStatus && hourStatus === workType[1]) {
      info = statusInfo.error;
    }
    if (hourStatus && !workType.includes(hourStatus)) {
      info = statusInfo.pending;
    }
    return info;
  };

  return { getStatusInfo };
};

// 咨询工时相关的hook
export const useConsultingTime = () => {
  const { getStatusInfo } = useStatusInfo();

  // 计算总工时
  const calculateTotalHours = (taskHourDetail?: TaskHourDetail[], fallbackHour?: string | number): number => {
    if (Array.isArray(taskHourDetail) && taskHourDetail.length > 0) {
      return taskHourDetail.reduce((sum, task) => sum + (Number(task.hour) || 0), 0);
    }
    return Number(fallbackHour) || 0;
  };

  // 获取任务状态信息
  const getTaskStatusInfo = (task: TaskHourDetail): StatusInfo => {
    return getStatusInfo(task);
  };

  return {
    calculateTotalHours,
    getTaskStatusInfo,
  };
};

// 工时验证相关的hook
export const useWorkTimeValidation = () => {
  // 检查是否可以添加工时
  const canAddWorkTime = (
    currentDayTotal: number, 
    todayAllProjectTime: number, 
    maxHours: number = 8
  ): boolean => {
    return currentDayTotal < maxHours && todayAllProjectTime < maxHours;
  };

  // 检查工时是否超额
  const isOverTime = (hours: number, maxHours: number = 8): boolean => {
    return hours >= maxHours;
  };

  return {
    canAddWorkTime,
    isOverTime,
  };
};
