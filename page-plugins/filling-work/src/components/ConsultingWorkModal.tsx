import React from "react";
import { <PERSON><PERSON>, Form, Input, <PERSON><PERSON>, Tooltip, Alert, Select } from "antd";
import { InfoCircleOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { ConsultingOption, ProjectData } from "../interface";

interface WorkHoursModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any, fn: any) => void;
  onDelete: (values: any, fn: any) => void;
  showDelete: boolean;
  projectData: ProjectData;
  consultingOption: ConsultingOption[];
}

const ConsultingWorkModal: React.FC<WorkHoursModalProps> = ({
  visible,
  onCancel,
  onOk,
  onDelete,
  showDelete,
  projectData,
  consultingOption,
}) => {
  const [form] = Form.useForm();

  const handleDeleteLocal = async () => {
    const values = await form.validateFields();
    onDelete({ ...values, key: projectData.key, taskId: projectData.taskId }, form.resetFields);
  };
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      console.log("handleOk===========11111111111111=", values);
      onOk({ ...values, key: projectData.key}, form.resetFields);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };
  return (
    <Modal
      title="录入咨询工时"
      visible={visible}
      onCancel={handleCancel}
      footer={[
        showDelete && projectData.checkStatus!=='HW2377' && (
          <Button key="delete" danger onClick={handleDeleteLocal}>
            删除
            <Tooltip
              placement="top"
              title="如遇填写错误但未提交的工时，可快捷删除该条工时记录"
              arrowPointAtCenter
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </Button>
        ),
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleOk}>
          暂存
        </Button>,
      ]}
      width={600}
    >
      <Alert
        message="填报工时后点击【暂存】数据只会暂时存储,需要点击表格底下的【提交工时】按扭才会真正的记录工时"
        type="warning"
      />
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          taskId: projectData.taskId,
          day: projectData.day,
          workHourType: projectData.workHourType,
          hour: projectData.hour,
          workDesc: projectData.workDesc,
        }}
      >
        <Form.Item label="任务名称" name="taskId" rules={[{ required: true, message: "请选择任务名称" }]}>
          <Select
            options={consultingOption}
            allowClear
            placeholder="请选择任务名称"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item label="工时日期" name="day">
          <Input disabled bordered={false} style={{ color: "#333" }} />
        </Form.Item>

        <Form.Item label="工时类型" name="workHourType">
          <Select disabled  bordered={false} style={{ color: "#333" }}>
            <Select.Option value="KQM1264">任务工时</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="工时(h)"
          name="hour"
          rules={[
            { required: true, message: "请输入工时" },
            {
              pattern: /^(0\.[5-9]|[1-7](\.\d)?|8(\.0)?)$/,
              message:
                "请输入工时，单位为小时(h)，工时范围0.5-8，请精确至小数点后一位，示例:1.5",
            },
          ]}
          tooltip={{
            title:
              "请输入工时，单位为小时(h)，工时范围0.5-8，请精确至小数点后一位，示例:1.5",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Input placeholder="请输入工时" />
        </Form.Item>

        <Form.Item
          label="工作内容"
          name="workDesc"
          rules={[
            {
              required: true,
              message: "请输入工作内容,字数范围在5-1000字",
              min: 5,
              max: 1000,
            },
          ]}
          tooltip={{
            title: "请输入工作内容，字数范围在5-1000字",
            icon: <InfoCircleOutlined />,
          }}
        >
          <Input.TextArea
            placeholder="请输入工作内容,字数范围在5-1000字"
            maxLength={1000}
            showCount
            rows={4}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConsultingWorkModal;
