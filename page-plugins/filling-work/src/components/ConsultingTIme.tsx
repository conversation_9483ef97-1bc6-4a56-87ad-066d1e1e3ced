import React from "react";
import { Tooltip, message } from "antd";
import { ConsultingTimeProps, TaskHourDetail, WorkItem } from "../interface";
import {
  useConsultingTime,
  useWorkTimeValidation,
  useWorkTimeConstants,
} from "../hooks/work";

const ConsultingTime: React.FC<ConsultingTimeProps> = ({
  statusItem,
  r,
  isDay,
  isButtonVisible,
  typeName,
  onTaskClick,
  onAddTask,
  todayAllProjectTime,
  holiday,
  eventDay,
  setIsTempStatus,
  validateDailyWorkTime,
}) => {
  const { type, workType } = useWorkTimeConstants();
  const { calculateTotalHours, getTaskStatusInfo } = useConsultingTime();
  const { canAddWorkTime, isOverTime } = useWorkTimeValidation();

  // 计算当日总工时（不包括咨询工时，避免重复计算）
  const calculateTotalDayTime = (
    date: string,
    excludeCurrentConsulting = false
  ) => {
    // 获取所有项目的工时，但排除咨询任务小组
    const workTime = todayAllProjectTime(date);

    // 从总工时中减去咨询工时，避免重复计算
    // todayAllProjectTime 已经包含了咨询工时，所以需要减去
    const currentConsultingHour = excludeCurrentConsulting
      ? 0
      : statusItem.hour
      ? Number(statusItem.hour)
      : 0;

    const adjustedWorkTime = workTime - currentConsultingHour;

    // 休假工时
    const holidaySum =
      (Array.isArray(holiday) ? holiday : []).find((i) => i.day === date)
        ?.hour ?? 0;

    // 事件工时
    const eventDaySum =
      (Array.isArray(eventDay) ? eventDay : []).find((i) => i.day === date)
        ?.hour ?? 0;

    // console.log("计算总工时============", date,adjustedWorkTime, holidaySum);
    // Number(adjustedWorkTime)
    return   Number(holidaySum) + Number(eventDaySum);
  };

  // 处理任务点击
  const handleTaskClick = async (task: TaskHourDetail) => {
    console.log(
      "handleTaskClick============",
      statusItem.checkStatus,
      workType[1],
      statusItem.isSent,
      typeName,
      type[1]
    );
    console.log("task============", task);
    if (
      ((task.checkStatus === workType[1] || task.isSent) &&
        typeName !== type[1]) ||
      !task.id
    ) {
      setIsTempStatus(true);

      await onTaskClick(task);
    }
    console.log("handleTaskClick============", task);
    // 设置暂存状态
  };

  // 处理添加新任务
  const handleAddTask = () => {
    // 使用统一的校验函数
    if (!validateDailyWorkTime(statusItem.day)) {
      message.error("今日已填写8小时,无法录入工时！");
      return;
    }
    // 设置暂存状态
    setIsTempStatus(false);

    onAddTask(r as WorkItem, statusItem);
  };

  // 计算总工时
  const totalHours = calculateTotalHours(
    statusItem.taskHourDetail,
    statusItem.hour
  );

  const hasNoTasks =
    !Array.isArray(statusItem.taskHourDetail) ||
    statusItem.taskHourDetail.length === 0;
  // 计算当前咨询任务的总工时（不包括当前正在编辑的）
  const currentConsultingTotal = Array.isArray(statusItem.taskHourDetail)
    ? statusItem.taskHourDetail.reduce(
        (sum: number, task: any) => sum + (Number(task.hour) || 0),
        0
      )
    : 0;

  // 计算除咨询工时外的其他工时
  const otherProjectsTime = calculateTotalDayTime(statusItem.day, true);
  console.log("是否可以添加canAddTask============", otherProjectsTime,statusItem);

  const canAddTask =
    canAddWorkTime(
      currentConsultingTotal, // 当前咨询任务的总工时
      otherProjectsTime // 其他项目的工时（不包括咨询工时）
    ) && validateDailyWorkTime(statusItem.day); // 使用统一的校验函数
  const isRestDay = typeName === type[1];
  const isTimeRestricted = isDay && !isButtonVisible && !isRestDay;

  if (isTimeRestricted) {
    return <div className="badgeStatus">18:00开放</div>;
  }

  // 如果没有任务且可以添加任务，显示添加按钮（与父组件的isAdd逻辑一致）
  if (hasNoTasks && canAddTask && !isRestDay) {
    return (
      <div
        className={"badgeStatus" + " " + statusItem.status}
        onClick={handleAddTask}
      >
        <div
          className="badgeStatus-value"
          style={{
            backgroundColor: "#81d3f8",
            cursor: validateDailyWorkTime(statusItem.day)
              ? "pointer"
              : "no-drop",
          }}
        >
          <div
            className="add"
            style={{
              cursor: validateDailyWorkTime(statusItem.day)
                ? "pointer"
                : "no-drop",
              color: "#5378ff",
            }}
          >
            +
          </div>
        </div>
      </div>
    );
  }

  // 如果有任务，显示每个任务的独立框框
  if (!hasNoTasks) {
    const taskList = statusItem.taskHourDetail!;
    const hasMultipleTasks = taskList.length > 1;

    return (
      <div className={"badgeStatus" + " " + statusItem.status}>
        {/* 休息日显示 */}
        {isRestDay && totalHours > 0 && (
          <div
            className="badgeStatus-title"
            style={{ backgroundColor: "#52c41a" }}
          >
            加班
          </div>
        )}

        {/* 超时提示 */}
        {/* {isOverTime(totalHours) && (
          <div
            className="badgeStatus-title"
            style={{ backgroundColor: "#ff4d4f", left: "0", right: "auto" }}
          >
            满额
          </div>
        )} */}

        {/* 显示所有任务，每个任务一个独立的小框框 */}
        <div
          style={{
            display: "flex",
            gap: "4px",
            alignItems: "center",
            justifyContent: "center",
            height: "100%",
          }}
        >
          {taskList.map((task, index) => {
            const info = getTaskStatusInfo(task);
            const tip = info.title ? (
              <Tooltip placement="topLeft" title={info.tip}>
                {/* <div
                  className="badgeStatus-title"
                  style={{
                    backgroundColor: info.backgroundColor,
                    position: "absolute",
                    top: "-2px",
                    right: "-2px",
                    zIndex: 10,
                  }}
                >
                  {info.title}
                </div> */}
              </Tooltip>
            ) : null;

            return (
              <div
                key={task.taskId || index}
                style={{ position: "relative" }}
                onClick={() => handleTaskClick(task)}
              >
                {tip}
                <div
                  className="badgeStatus-value"
                  style={{
                    backgroundColor: info.backgroundColor,
                    cursor: !isRestDay && info.click ? "pointer" : "default",
                    width: hasMultipleTasks ? "35px" : "45px",
                    height: hasMultipleTasks ? "35px" : "45px",
                    lineHeight: hasMultipleTasks ? "33px" : "44px",
                    fontSize: hasMultipleTasks ? "12px" : "14px",
                  }}
                >
                  <Tooltip
                    title={`任务: ${task.taskName}\n描述: ${
                      task.workDesc || "无"
                    }\n工时: ${task.hour}h`}
                  >
                    <div>{task.hour}h</div>
                  </Tooltip>
                </div>
              </div>
            );
          })}
        </div>

        {/* 如果还能添加任务，在右下角显示小的添加按钮 */}
        {canAddTask && !isRestDay && (
          <div
            style={{
              position: "absolute",
              bottom: "2px",
              right: "2px",
              width: "32px",
              height: "32px",
              backgroundColor: "#81d3f8",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              fontSize: "24px",
              color: "#5378ff",
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleAddTask();
            }}
          >
            +
          </div>
        )}
      </div>
    );
  }

  // 默认空状态
  return (
    <div
      className="badgeStatus"
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div
        style={{
          backgroundColor: "#d7d7d7",
          borderRadius: "50%",
          width: "45px",
          height: "45px",
          lineHeight: "45px",
          fontSize: "14px",
          color: "#fff",
        }}
      >
        0h
      </div>
    </div>
  );
};

export default ConsultingTime;
