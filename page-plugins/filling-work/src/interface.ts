export interface WorkItem {
    projectState: {
      apiId: string;
    };
    key: number;
    department: string;
    projectType: string;
    projectName: string;
    projectId: string;
    days: StatusItem[];
  }
  export interface Winfo {
    projectName: string;
    day: string;
    workHourType: string;
    hour: string;
    workDesc: string;
    key: number;
    taskId?: string; // 咨询任务的唯一标识符
  }
  export interface StatusItem {
    day: string;
    status: string;
    hour: string;
    id: string;
    checkStatus: string;
    leaveType: string;
    workDesc: string;
    workHourType: string;
    isSent?: boolean;
    dayType?: string;
    taskHourDetail?: TaskHourDetail[];
  }

  export interface WorkHoursModalProps {
    visible: boolean;
    onCancel: () => void;
    onOk: (values: any, fn: any) => void;
    onDelete: (values: any, fn: any) => void;
    showDelete: boolean;
    projectData: ProjectData;
  }

  // 咨询工时相关类型定义
  export interface TaskHourDetail {
    id: string | null;
    day: string;
    dayType: string;
    hour: string | number;
    checkStatus: string | null;
    leaveType: string;
    workHourType: string;
    workDesc: string;
    taskId: string;
    taskName: string;
    isSent: boolean;
  }

  export interface ConsultingTimeProps {
    statusItem: StatusItem;
    r: WorkItem;
    isDay: boolean;
    isButtonVisible: boolean;
    typeName: string;
    onTaskClick: (task: TaskHourDetail) => Promise<void>;
    onAddTask: (r: WorkItem, statusItem: StatusItem) => void;
    todayAllProjectTime: (date: string) => number;
    holiday: Array<{ day: string; hour: number }>;
    eventDay: Array<{ day: string; hour: number }>;
    setIsTempStatus: (status: boolean) => void;
    validateDailyWorkTime: (date: string, newHour?: number, excludeHour?: number) => boolean;
  }

  // 状态信息类型
  export interface StatusInfo {
    backgroundColor: string;
    backRaund?: string;
    title?: string;
    tip?: string;
    click?: string;
  }

  // 工时配置类型
  export interface WorkTimeConstants {
    workType: string[];
    type: string[];
    statusInfo: Record<string, StatusInfo>;
  }
  
  export interface ConsultingOption {
    label: string;
    value: string;
    disabled?: boolean;
  }

  export interface ProjectData {
    projectName?: string;
    id?: string | null;
    day: string;
    dayType?: string;
    hour: string | number;
    checkStatus?: string | null;
    leaveType?: string;
    workHourType: string;
    workDesc: string;
    key?: number;
    taskId?: string;
    taskName?: string;
    isSent?: boolean;
    [key: string]: any;
  }