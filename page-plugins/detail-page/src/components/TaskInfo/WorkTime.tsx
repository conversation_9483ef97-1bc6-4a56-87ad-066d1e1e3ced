import React from "react";
import { Popover, Table, Tooltip } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

interface WorkflowItem {
  id: string;
  type: string;
  operator: string;
  time: string;
  name: string;
  status: string;
  executorEmployeeId: string;
  urgentLevel: string;
  urgentLevelValue: string;
  tworkDate: string;
  tworkDesc: string;
  tworkingHours: number;
}

interface WorkflowInfoProps {
  workflow: WorkflowItem[];
  columnType: string;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number, pageSize?: number) => void;
  };
  loading?: boolean;
}

const WorkTime: React.FC<WorkflowInfoProps> = ({ 
  workflow, 
  columnType, 
  pagination,
  loading = false 
}) => {

  // 这个是操作列表的
  const columns: ColumnsType<WorkflowItem> = [
    {
      title: "实际执行日期",
      dataIndex: "tworkDate",
      key: "tworkDate",
      render: (text) => <span className="text-blue-500">{dayjs(text).format("YYYY-MM-DD")}</span>,
    },
    {
      title: "工作内容",
      dataIndex: "tworkDesc",
      key: "tworkDesc",
      ellipsis: true,
      width: '100px',
      render: (text) => {
        return <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      }
    },
    {
      title: "实际工时",
      dataIndex: "tworkingHours",
      key: "tworkingHours",
      render: (text) => {
        return <span>{text}h</span>
      }
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={workflow}
      pagination={pagination ? {
        current: pagination.current,
        total: pagination.total,
        pageSize: pagination.pageSize,
        showSizeChanger: false,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条`,
        onChange: pagination.onChange,
      } : false}
      rowKey={(record, index) => record.id || index.toString()}
      size="small"
      className="text-sm"
      loading={loading}
    />
  );
};

export default WorkTime;
